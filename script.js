// Global variables
let map;
let directionsService;
let directionsRenderer;
let routeData = [];
let markers = [];
let animationMarker;
let routePath = [];
let completedRoutePolyline;
let animationState = {
    isAnimating: false,
    isPaused: false,
    currentStep: 0,
    totalSteps: 0,
    speed: 1,
    animationId: null
};

// Voice-related variables
let speechSynthesis = window.speechSynthesis;
let speechRecognition;
let voiceEnabled = false;
let narrationEnabled = false;
let isListening = false;

// Sample route data - Kerala locations
const sampleData = [
    {
        name: "Sree Maha Ganapathi Temple, Kottarakkara",
        lat: 9.001029451672705,
        lng: 76.77008521036686,
        description: "Sacred Hindu temple dedicated to <PERSON><PERSON><PERSON>, located in Kottarakkara, Kerala"
    },
    {
        name: "Lulu Mall, Trivandrum",
        lat: 8.51550002882469,
        lng: 76.8976382508458,
        description: "Popular shopping mall and entertainment destination in Thiruvananthapuram, Kerala"
    },
    {
        name: "Kollam Railway Station",
        lat: 8.886214793056155,
        lng: 76.59508807968544,
        description: "Major railway junction connecting Kollam to various cities across India"
    },
    {
        name: "Elampaloor",
        lat: 8.955887655784867,
        lng: 76.67080925269629,
        description: "Locality in Kerala, known for its scenic beauty and cultural significance"
    }
];

// Initialize the map
function initMap() {
    hideLoading();

    // Initialize map with configuration settings
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: CONFIG.map.defaultZoom,
        center: CONFIG.map.defaultCenter,
        mapTypeId: 'roadmap',
        styles: CONFIG.map.styles,
        maxZoom: CONFIG.map.maxZoom,
        minZoom: CONFIG.map.minZoom
    });

    // Initialize directions service and renderer
    directionsService = new google.maps.DirectionsService();
    directionsRenderer = new google.maps.DirectionsRenderer({
        suppressMarkers: true,
        polylineOptions: CONFIG.route.mainRoute
    });
    directionsRenderer.setMap(map);

    // Initialize event listeners
    initEventListeners();

    // Initialize speed slider with config values
    const speedSlider = document.getElementById('speedSlider');
    speedSlider.min = CONFIG.animation.minSpeed;
    speedSlider.max = CONFIG.animation.maxSpeed;
    speedSlider.step = CONFIG.animation.speedStep;
    speedSlider.value = CONFIG.animation.defaultSpeed;
    animationState.speed = CONFIG.animation.defaultSpeed;

    // Initialize voice features
    initVoiceFeatures();

    // Auto-load Kerala locations by default
    autoLoadDefaultLocations();

    showMessage(CONFIG.messages.success.mapInit, 'success');
}

// Initialize all event listeners
function initEventListeners() {
    // Animation controls
    document.getElementById('startBtn').addEventListener('click', startAnimation);
    document.getElementById('pauseBtn').addEventListener('click', pauseAnimation);
    document.getElementById('resumeBtn').addEventListener('click', resumeAnimation);
    document.getElementById('resetBtn').addEventListener('click', resetAnimation);
    
    // Speed control
    const speedSlider = document.getElementById('speedSlider');
    speedSlider.addEventListener('input', (e) => {
        animationState.speed = parseFloat(e.target.value);
        document.getElementById('speedValue').textContent = `${animationState.speed}x`;
    });
    
    // Map controls
    document.getElementById('fitBoundsBtn').addEventListener('click', fitMapToBounds);
    document.getElementById('followBtn').addEventListener('click', toggleFollowMode);
    document.getElementById('toggleViewBtn').addEventListener('click', toggleMapType);
    
    // Data controls
    document.getElementById('loadSampleBtn').addEventListener('click', loadSampleData);
    document.getElementById('loadCustomBtn').addEventListener('click', () => {
        document.getElementById('fileInput').click();
    });
    document.getElementById('fileInput').addEventListener('change', handleFileUpload);

    // Voice controls
    document.getElementById('voiceToggleBtn').addEventListener('click', toggleVoiceRecognition);
    document.getElementById('narrateToggleBtn').addEventListener('click', toggleNarration);
}

// Auto-load default Kerala locations on map initialization
function autoLoadDefaultLocations() {
    showLoading('Loading Kerala locations...');
    routeData = [...sampleData];
    displayRoutePoints();
    calculateAndDisplayRoute();
}

// Load sample data (manual trigger)
function loadSampleData() {
    showLoading('Reloading Kerala route data...');
    routeData = [...sampleData];
    displayRoutePoints();
    calculateAndDisplayRoute();
}

// Handle custom file upload
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (file.type !== 'application/json') {
        showMessage('Please select a valid JSON file.', 'error');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
        try {
            const data = JSON.parse(e.target.result);
            if (validateRouteData(data)) {
                showLoading('Loading custom route data...');
                routeData = data;
                displayRoutePoints();
                calculateAndDisplayRoute();
            } else {
                showMessage('Invalid route data format. Please check the documentation.', 'error');
            }
        } catch (error) {
            showMessage('Error parsing JSON file: ' + error.message, 'error');
        }
    };
    reader.readAsText(file);
}

// Validate route data format
function validateRouteData(data) {
    if (!Array.isArray(data)) {
        return false;
    }

    if (data.length < CONFIG.validation.minRoutePoints) {
        showMessage(CONFIG.messages.errors.minPoints, 'error');
        return false;
    }

    if (data.length > CONFIG.validation.maxRoutePoints) {
        showMessage(CONFIG.messages.errors.maxPoints, 'error');
        return false;
    }

    const isValid = data.every(point =>
        point.hasOwnProperty('name') &&
        point.hasOwnProperty('lat') &&
        point.hasOwnProperty('lng') &&
        typeof point.lat === 'number' &&
        typeof point.lng === 'number' &&
        point.lat >= CONFIG.validation.latBounds.min &&
        point.lat <= CONFIG.validation.latBounds.max &&
        point.lng >= CONFIG.validation.lngBounds.min &&
        point.lng <= CONFIG.validation.lngBounds.max
    );

    if (!isValid) {
        showMessage(CONFIG.messages.errors.invalidCoords, 'error');
    }

    return isValid;
}

// Display route points in the UI
function displayRoutePoints() {
    const container = document.getElementById('routePoints');
    
    if (routeData.length === 0) {
        container.innerHTML = '<p class="no-data">No route data loaded.</p>';
        return;
    }
    
    container.innerHTML = routeData.map((point, index) => `
        <div class="route-point">
            <div class="route-point-number">${index + 1}</div>
            <div class="route-point-info">
                <h5>${point.name}</h5>
                <p>${point.description || `Lat: ${point.lat}, Lng: ${point.lng}`}</p>
            </div>
        </div>
    `).join('');
}

// Calculate and display route using Google Directions API
function calculateAndDisplayRoute() {
    if (routeData.length < 2) {
        showMessage('At least 2 points are required for a route.', 'error');
        hideLoading();
        return;
    }
    
    clearMarkers();
    
    const waypoints = routeData.slice(1, -1).map(point => ({
        location: new google.maps.LatLng(point.lat, point.lng),
        stopover: true
    }));
    
    const request = {
        origin: new google.maps.LatLng(routeData[0].lat, routeData[0].lng),
        destination: new google.maps.LatLng(routeData[routeData.length - 1].lat, routeData[routeData.length - 1].lng),
        waypoints: waypoints,
        travelMode: google.maps.TravelMode.DRIVING,
        optimizeWaypoints: false
    };
    
    directionsService.route(request, (result, status) => {
        hideLoading();
        
        if (status === 'OK') {
            directionsRenderer.setDirections(result);
            createMarkers();
            extractRoutePath(result);
            fitMapToBounds();
            showMessage(CONFIG.messages.success.routeCalc, 'success');

            // Voice narration for route loaded
            const narrationText = CONFIG.narration.routeLoaded.replace('{count}', routeData.length);
            speak(narrationText);

            // Enable start button
            document.getElementById('startBtn').disabled = false;
        } else {
            showMessage('Error calculating route: ' + status, 'error');
        }
    });
}

// Create markers for all route points
function createMarkers() {
    // First, create red markers for all JSON-specified locations
    routeData.forEach((point, index) => {
        const redMarker = new google.maps.Marker({
            position: { lat: point.lat, lng: point.lng },
            map: map,
            title: `${point.name} (Specified Location)`,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: CONFIG.markers.jsonLocation.scale,
                fillColor: CONFIG.markers.jsonLocation.fillColor,
                fillOpacity: 1,
                strokeColor: CONFIG.markers.jsonLocation.strokeColor,
                strokeWeight: CONFIG.markers.jsonLocation.strokeWeight
            },
            zIndex: CONFIG.markers.jsonLocation.zIndex
        });

        // Create info window for red marker
        const redInfoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h3 style="margin: 0 0 8px 0; color: #dc2626; font-weight: bold;">📍 ${point.name}</h3>
                    <p style="margin: 0 0 5px 0; color: #718096; font-style: italic;">Specified Location #${index + 1}</p>
                    <p style="margin: 0; color: #718096;">${point.description || 'No description available'}</p>
                    <p style="margin: 5px 0 0 0; font-size: 0.8em; color: #a0aec0;">
                        ${point.lat.toFixed(4)}, ${point.lng.toFixed(4)}
                    </p>
                </div>
            `
        });

        redMarker.addListener('click', () => {
            redInfoWindow.open(map, redMarker);
        });

        markers.push(redMarker);
    });

    // Then create route markers (start, waypoint, end) with smaller size
    routeData.forEach((point, index) => {
        const isStart = index === 0;
        const isEnd = index === routeData.length - 1;

        let markerConfig;
        if (isStart) {
            markerConfig = CONFIG.markers.start;
        } else if (isEnd) {
            markerConfig = CONFIG.markers.end;
        } else {
            markerConfig = CONFIG.markers.waypoint;
        }

        const routeMarker = new google.maps.Marker({
            position: { lat: point.lat, lng: point.lng },
            map: map,
            title: `${point.name} (${isStart ? 'Start' : isEnd ? 'End' : 'Waypoint'})`,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: markerConfig.scale,
                fillColor: markerConfig.fillColor,
                fillOpacity: 1,
                strokeColor: markerConfig.strokeColor,
                strokeWeight: markerConfig.strokeWeight
            },
            zIndex: markerConfig.zIndex
        });
        
        // Create info window for route marker
        const routeInfoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h3 style="margin: 0 0 8px 0; color: #2d3748;">${point.name}</h3>
                    <p style="margin: 0 0 5px 0; color: #718096; font-weight: bold;">
                        ${isStart ? '🟢 Start Point' : isEnd ? '🔴 End Point' : '🔵 Waypoint'}
                    </p>
                    <p style="margin: 0; color: #718096;">${point.description || 'No description available'}</p>
                    <p style="margin: 5px 0 0 0; font-size: 0.8em; color: #a0aec0;">
                        ${point.lat.toFixed(4)}, ${point.lng.toFixed(4)}
                    </p>
                </div>
            `
        });

        routeMarker.addListener('click', () => {
            routeInfoWindow.open(map, routeMarker);
        });

        markers.push(routeMarker);
    });
}

// Extract route path from directions result
function extractRoutePath(directionsResult) {
    routePath = [];
    const route = directionsResult.routes[0];
    
    route.legs.forEach(leg => {
        leg.steps.forEach(step => {
            const stepPath = step.path || [step.start_location, step.end_location];
            routePath.push(...stepPath);
        });
    });
    
    animationState.totalSteps = routePath.length;
    updateProgress(0);
}

// Start animation
function startAnimation() {
    if (routePath.length === 0) {
        showMessage(CONFIG.messages.errors.loadData, 'error');
        speak(CONFIG.messages.errors.loadData);
        return;
    }

    animationState.isAnimating = true;
    animationState.isPaused = false;
    animationState.currentStep = 0;
    animationState.lastNarratedProgress = 0;

    // Update button states
    document.getElementById('startBtn').disabled = true;
    document.getElementById('pauseBtn').disabled = false;
    document.getElementById('resumeBtn').disabled = true;

    // Voice narration for animation start
    if (routeData.length > 0) {
        const startLocation = routeData[0].name;
        const endLocation = routeData[routeData.length - 1].name;
        const narrationText = CONFIG.narration.animationStart
            .replace('{start}', startLocation)
            .replace('{end}', endLocation);
        speak(narrationText);
    }
    
    // Create animation marker
    if (animationMarker) {
        animationMarker.setMap(null);
    }
    
    animationMarker = new google.maps.Marker({
        position: routePath[0],
        map: map,
        icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: CONFIG.markers.animated.scale,
            fillColor: CONFIG.markers.animated.fillColor,
            fillOpacity: 1,
            strokeColor: CONFIG.markers.animated.strokeColor,
            strokeWeight: CONFIG.markers.animated.strokeWeight
        },
        zIndex: CONFIG.markers.animated.zIndex
    });
    
    animate();
}

// Animation loop
function animate() {
    if (!animationState.isAnimating || animationState.isPaused) {
        return;
    }

    if (animationState.currentStep >= routePath.length - 1) {
        completeAnimation();
        return;
    }

    // Update marker position
    const currentPosition = routePath[animationState.currentStep];
    animationMarker.setPosition(currentPosition);

    // Follow mode - center map on animated marker
    if (followMode && animationMarker) {
        map.panTo(currentPosition);
    }

    // Update progress
    const progress = (animationState.currentStep / (routePath.length - 1)) * 100;
    updateProgress(progress);

    // Update current location info
    const nearestPoint = findNearestRoutePoint(currentPosition);
    document.getElementById('currentLocation').textContent = `Near ${nearestPoint.name}`;

    // Voice narration for location updates (every 25% progress)
    const progressPercent = Math.floor(progress);
    if (narrationEnabled && progressPercent > 0 && progressPercent % 25 === 0) {
        const lastNarrated = animationState.lastNarratedProgress || 0;
        if (progressPercent > lastNarrated) {
            const narrationText = CONFIG.narration.progressUpdate.replace('{percent}', progressPercent);
            speak(narrationText);
            animationState.lastNarratedProgress = progressPercent;
        }
    }

    // Update completed route visualization
    updateCompletedRoute(animationState.currentStep);

    animationState.currentStep += Math.ceil(animationState.speed);

    // Schedule next frame
    animationState.animationId = setTimeout(() => {
        requestAnimationFrame(animate);
    }, CONFIG.animation.frameRate / animationState.speed);
}

// Find nearest route point to current position
function findNearestRoutePoint(currentPos) {
    let minDistance = Infinity;
    let nearestPoint = routeData[0];
    
    routeData.forEach(point => {
        const distance = google.maps.geometry.spherical.computeDistanceBetween(
            currentPos,
            new google.maps.LatLng(point.lat, point.lng)
        );
        
        if (distance < minDistance) {
            minDistance = distance;
            nearestPoint = point;
        }
    });
    
    return nearestPoint;
}

// Pause animation
function pauseAnimation() {
    animationState.isPaused = true;

    if (animationState.animationId) {
        clearTimeout(animationState.animationId);
    }

    document.getElementById('pauseBtn').disabled = true;
    document.getElementById('resumeBtn').disabled = false;

    speak(CONFIG.narration.animationPause);
}

// Resume animation
function resumeAnimation() {
    animationState.isPaused = false;

    document.getElementById('pauseBtn').disabled = false;
    document.getElementById('resumeBtn').disabled = true;

    speak(CONFIG.narration.animationResume);
    animate();
}

// Reset animation
function resetAnimation() {
    animationState.isAnimating = false;
    animationState.isPaused = false;
    animationState.currentStep = 0;

    if (animationState.animationId) {
        clearTimeout(animationState.animationId);
    }

    if (animationMarker) {
        animationMarker.setMap(null);
        animationMarker = null;
    }

    if (completedRoutePolyline) {
        completedRoutePolyline.setMap(null);
        completedRoutePolyline = null;
    }

    // Reset button states
    document.getElementById('startBtn').disabled = false;
    document.getElementById('pauseBtn').disabled = true;
    document.getElementById('resumeBtn').disabled = true;

    // Reset progress
    updateProgress(0);
    document.getElementById('currentLocation').textContent = 'Ready to start';

    speak(CONFIG.narration.animationReset);
}

// Complete animation
function completeAnimation() {
    animationState.isAnimating = false;
    updateProgress(100);
    document.getElementById('currentLocation').textContent = 'Animation completed!';

    // Reset button states
    document.getElementById('startBtn').disabled = false;
    document.getElementById('pauseBtn').disabled = true;
    document.getElementById('resumeBtn').disabled = true;

    showMessage(CONFIG.messages.success.animationComplete, 'success');

    // Voice narration for completion
    if (routeData.length > 0) {
        const startLocation = routeData[0].name;
        const endLocation = routeData[routeData.length - 1].name;
        const narrationText = CONFIG.narration.animationComplete
            .replace('{start}', startLocation)
            .replace('{end}', endLocation);
        speak(narrationText);
    }
}

// Update progress display
function updateProgress(percentage) {
    document.getElementById('progressFill').style.width = `${percentage}%`;
    document.getElementById('progressText').textContent = `${Math.round(percentage)}%`;
}

// Update completed route visualization
function updateCompletedRoute(currentStep) {
    if (completedRoutePolyline) {
        completedRoutePolyline.setMap(null);
    }

    if (currentStep > 0) {
        const completedPath = routePath.slice(0, currentStep + 1);

        completedRoutePolyline = new google.maps.Polyline({
            path: completedPath,
            geodesic: true,
            strokeColor: CONFIG.route.completedRoute.strokeColor,
            strokeOpacity: CONFIG.route.completedRoute.strokeOpacity,
            strokeWeight: CONFIG.route.completedRoute.strokeWeight,
            zIndex: CONFIG.route.completedRoute.zIndex
        });

        completedRoutePolyline.setMap(map);
    }
}

// Fit map to show all markers
function fitMapToBounds() {
    if (markers.length === 0) return;
    
    const bounds = new google.maps.LatLngBounds();
    markers.forEach(marker => {
        bounds.extend(marker.getPosition());
    });
    
    map.fitBounds(bounds);
    
    // Ensure minimum zoom level
    google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
        if (map.getZoom() > 15) {
            map.setZoom(15);
        }
    });
}

// Toggle follow mode (center map on animated marker)
let followMode = false;
function toggleFollowMode() {
    followMode = !followMode;
    const btn = document.getElementById('followBtn');

    if (followMode) {
        btn.innerHTML = '<i class="fas fa-crosshairs"></i> Following';
        btn.style.background = '#4299e1';
        btn.style.color = 'white';

        speak(CONFIG.narration.followModeOn);

        if (animationMarker) {
            map.setCenter(animationMarker.getPosition());
            map.setZoom(CONFIG.animation.followZoom);
        }
    } else {
        btn.innerHTML = '<i class="fas fa-crosshairs"></i> Follow';
        btn.style.background = 'transparent';
        btn.style.color = '#4a5568';

        speak(CONFIG.narration.followModeOff);
    }
}

// Toggle map type
let isRoadmap = true;
function toggleMapType() {
    const btn = document.getElementById('toggleViewBtn');

    if (isRoadmap) {
        map.setMapTypeId('satellite');
        btn.innerHTML = '<i class="fas fa-map"></i> Roadmap';
        speak(CONFIG.narration.mapTypeChange.replace('{type}', 'satellite'));
        isRoadmap = false;
    } else {
        map.setMapTypeId('roadmap');
        btn.innerHTML = '<i class="fas fa-satellite"></i> Satellite';
        speak(CONFIG.narration.mapTypeChange.replace('{type}', 'roadmap'));
        isRoadmap = true;
    }
}

// Clear all markers
function clearMarkers() {
    markers.forEach(marker => marker.setMap(null));
    markers = [];
    
    if (animationMarker) {
        animationMarker.setMap(null);
        animationMarker = null;
    }
}

// Utility functions
function showLoading(message = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const text = overlay.querySelector('p');
    text.textContent = message;
    overlay.classList.add('show');
}

function hideLoading() {
    document.getElementById('loadingOverlay').classList.remove('show');
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    container.appendChild(messageDiv);
    
    // Auto remove after configured time
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, CONFIG.ui.messageDisplayTime);
}

// Initialize voice features
function initVoiceFeatures() {
    // Check for speech synthesis support
    if (!speechSynthesis) {
        console.warn('Speech synthesis not supported');
        document.getElementById('narrateToggleBtn').disabled = true;
        return;
    }

    // Check for speech recognition support
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
        console.warn('Speech recognition not supported');
        document.getElementById('voiceToggleBtn').disabled = true;
        showMessage(CONFIG.messages.errors.voiceNotSupported, 'error');
        return;
    }

    // Initialize speech recognition
    speechRecognition = new SpeechRecognition();
    speechRecognition.continuous = CONFIG.voice.recognition.continuous;
    speechRecognition.interimResults = CONFIG.voice.recognition.interimResults;
    speechRecognition.lang = CONFIG.voice.recognition.lang;
    speechRecognition.maxAlternatives = CONFIG.voice.recognition.maxAlternatives;

    // Speech recognition event handlers
    speechRecognition.onstart = () => {
        isListening = true;
        updateVoiceStatus('Listening for commands...');
        document.getElementById('voiceToggleBtn').classList.add('voice-listening');
    };

    speechRecognition.onend = () => {
        isListening = false;
        if (voiceEnabled) {
            // Restart recognition if it's still enabled
            setTimeout(() => {
                if (voiceEnabled) {
                    speechRecognition.start();
                }
            }, 100);
        } else {
            updateVoiceStatus('Voice commands disabled');
            document.getElementById('voiceToggleBtn').classList.remove('voice-listening');
        }
    };

    speechRecognition.onresult = (event) => {
        const result = event.results[event.results.length - 1];
        if (result.isFinal) {
            const command = result[0].transcript.toLowerCase().trim();
            processVoiceCommand(command);
        }
    };

    speechRecognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        if (event.error === 'not-allowed') {
            showMessage(CONFIG.messages.errors.microphonePermission, 'error');
            voiceEnabled = false;
            updateVoiceToggleButton();
        }
    };
}

// Toggle voice recognition
function toggleVoiceRecognition() {
    if (!speechRecognition) {
        showMessage(CONFIG.messages.errors.voiceNotSupported, 'error');
        return;
    }

    voiceEnabled = !voiceEnabled;

    if (voiceEnabled) {
        try {
            speechRecognition.start();
            showMessage(CONFIG.messages.success.voiceEnabled, 'success');
        } catch (error) {
            console.error('Error starting speech recognition:', error);
            voiceEnabled = false;
        }
    } else {
        speechRecognition.stop();
        showMessage(CONFIG.messages.success.voiceDisabled, 'info');
    }

    updateVoiceToggleButton();
}

// Toggle narration
function toggleNarration() {
    narrationEnabled = !narrationEnabled;

    if (narrationEnabled) {
        showMessage(CONFIG.messages.success.narrationEnabled, 'success');
        speak(CONFIG.messages.success.narrationEnabled);
    } else {
        showMessage(CONFIG.messages.success.narrationDisabled, 'info');
        // Stop any current speech
        speechSynthesis.cancel();
    }

    updateNarrationToggleButton();
}

// Update voice toggle button appearance
function updateVoiceToggleButton() {
    const btn = document.getElementById('voiceToggleBtn');
    if (voiceEnabled) {
        btn.innerHTML = '<i class="fas fa-microphone"></i> Disable Voice';
        btn.classList.add('btn-primary');
        btn.classList.remove('btn-outline');
        updateVoiceStatus('Voice commands enabled');
    } else {
        btn.innerHTML = '<i class="fas fa-microphone-slash"></i> Enable Voice';
        btn.classList.remove('btn-primary', 'voice-listening');
        btn.classList.add('btn-outline');
        updateVoiceStatus('Voice commands disabled');
    }
}

// Update narration toggle button appearance
function updateNarrationToggleButton() {
    const btn = document.getElementById('narrateToggleBtn');
    if (narrationEnabled) {
        btn.innerHTML = '<i class="fas fa-volume-up"></i> Disable Narration';
        btn.classList.add('btn-primary');
        btn.classList.remove('btn-outline');
    } else {
        btn.innerHTML = '<i class="fas fa-volume-mute"></i> Enable Narration';
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline');
    }
}

// Update voice status display
function updateVoiceStatus(status) {
    document.getElementById('voiceStatus').textContent = status;
}

// Initialize voice features
function initVoiceFeatures() {
    // Check for speech synthesis support
    if (!speechSynthesis) {
        console.warn('Speech synthesis not supported');
        document.getElementById('narrateToggleBtn').disabled = true;
        return;
    }

    // Check for speech recognition support
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
        console.warn('Speech recognition not supported');
        document.getElementById('voiceToggleBtn').disabled = true;
        showMessage(CONFIG.messages.errors.voiceNotSupported, 'error');
        return;
    }

    // Initialize speech recognition
    speechRecognition = new SpeechRecognition();
    speechRecognition.continuous = CONFIG.voice.recognition.continuous;
    speechRecognition.interimResults = CONFIG.voice.recognition.interimResults;
    speechRecognition.lang = CONFIG.voice.recognition.lang;
    speechRecognition.maxAlternatives = CONFIG.voice.recognition.maxAlternatives;

    // Speech recognition event handlers
    speechRecognition.onstart = () => {
        isListening = true;
        updateVoiceStatus('Listening for commands...');
        document.getElementById('voiceToggleBtn').classList.add('voice-listening');
    };

    speechRecognition.onend = () => {
        isListening = false;
        if (voiceEnabled) {
            setTimeout(() => {
                if (voiceEnabled) {
                    try {
                        speechRecognition.start();
                    } catch (error) {
                        console.error('Error restarting speech recognition:', error);
                    }
                }
            }, 100);
        } else {
            updateVoiceStatus('Voice commands disabled');
            document.getElementById('voiceToggleBtn').classList.remove('voice-listening');
        }
    };

    speechRecognition.onresult = (event) => {
        const result = event.results[event.results.length - 1];
        if (result.isFinal) {
            const command = result[0].transcript.toLowerCase().trim();
            processVoiceCommand(command);
        }
    };

    speechRecognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        if (event.error === 'not-allowed') {
            showMessage(CONFIG.messages.errors.microphonePermission, 'error');
            voiceEnabled = false;
            updateVoiceToggleButton();
        }
    };
}

// Handle window resize
window.addEventListener('resize', () => {
    if (map) {
        google.maps.event.trigger(map, 'resize');
    }
});

// Process voice commands
function processVoiceCommand(command) {
    console.log('Voice command received:', command);
    updateVoiceStatus(`Processing: "${command}"`);

    // Find matching command
    let matchedCommand = null;
    for (const [phrase, action] of Object.entries(CONFIG.voice.commands)) {
        if (command.includes(phrase)) {
            matchedCommand = action;
            break;
        }
    }

    if (matchedCommand) {
        executeVoiceCommand(matchedCommand, command);
        speak(`Executing ${command}`);
    } else {
        showMessage(CONFIG.messages.errors.voiceCommandNotRecognized, 'error');
        speak('Command not recognized. Try again.');
    }

    setTimeout(() => {
        updateVoiceStatus('Listening for commands...');
    }, 2000);
}

// Execute voice command
function executeVoiceCommand(action, originalCommand) {
    switch(action) {
        case 'startAnimation':
            startAnimation();
            break;
        case 'pauseAnimation':
            pauseAnimation();
            break;
        case 'resumeAnimation':
            resumeAnimation();
            break;
        case 'resetAnimation':
            resetAnimation();
            break;
        case 'fitMapToBounds':
            fitMapToBounds();
            break;
        case 'toggleFollowMode':
            toggleFollowMode();
            break;
        case 'toggleMapType':
            toggleMapType();
            break;
        case 'increaseSpeed':
            changeSpeed(0.5);
            break;
        case 'decreaseSpeed':
            changeSpeed(-0.5);
            break;
        case 'loadSampleData':
            loadSampleData();
            break;
        case 'zoomIn':
            zoomIn();
            break;
        case 'zoomOut':
            zoomOut();
            break;
        case 'centerMap':
            centerMap();
            break;
        case 'enableNarration':
            if (!narrationEnabled) toggleNarration();
            break;
        case 'disableNarration':
            if (narrationEnabled) toggleNarration();
            break;
        case 'announceLocation':
            announceCurrentLocation();
            break;
        case 'announceSpeed':
            announceCurrentSpeed();
            break;
        case 'announceProgress':
            announceCurrentProgress();
            break;
        case 'announceStatus':
            announceAnimationStatus();
            break;
        case 'announceLocations':
            announceLoadedLocations();
            break;
        default:
            console.warn('Unknown voice command action:', action);
    }
}

// Change animation speed via voice
function changeSpeed(delta) {
    const newSpeed = Math.max(CONFIG.animation.minSpeed,
                             Math.min(CONFIG.animation.maxSpeed,
                                     animationState.speed + delta));

    if (newSpeed !== animationState.speed) {
        animationState.speed = newSpeed;
        document.getElementById('speedSlider').value = newSpeed;
        document.getElementById('speedValue').textContent = `${newSpeed}x`;

        const narrationText = CONFIG.narration.speedChange.replace('{speed}', `${newSpeed} times`);
        speak(narrationText);
    }
}

// Text-to-speech function
function speak(text) {
    if (!narrationEnabled || !speechSynthesis) return;

    // Cancel any ongoing speech
    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = CONFIG.voice.speech.rate;
    utterance.pitch = CONFIG.voice.speech.pitch;
    utterance.volume = CONFIG.voice.speech.volume;
    utterance.lang = CONFIG.voice.speech.lang;

    speechSynthesis.speak(utterance);
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Only handle shortcuts when not typing in input fields
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
    }

    switch(e.key) {
        case ' ': // Spacebar - Start/Pause
            e.preventDefault();
            if (!animationState.isAnimating) {
                startAnimation();
            } else if (!animationState.isPaused) {
                pauseAnimation();
            } else {
                resumeAnimation();
            }
            break;
        case 'r': // R - Reset
        case 'R':
            e.preventDefault();
            resetAnimation();
            break;
        case 'f': // F - Fit bounds
        case 'F':
            e.preventDefault();
            fitMapToBounds();
            break;
        case 'Escape': // Escape - Reset
            e.preventDefault();
            resetAnimation();
            break;
    }
});

// Error handling for Google Maps API
window.gm_authFailure = function() {
    showMessage(CONFIG.messages.errors.apiKey, 'error');
    hideLoading();
};

// Handle network errors
window.addEventListener('online', () => {
    showMessage(CONFIG.messages.success.connectionRestored, 'success');
});

window.addEventListener('offline', () => {
    showMessage(CONFIG.messages.info.connectionLost, 'error');
});

// Toggle voice recognition
function toggleVoiceRecognition() {
    if (!speechRecognition) {
        showMessage(CONFIG.messages.errors.voiceNotSupported, 'error');
        return;
    }

    voiceEnabled = !voiceEnabled;

    if (voiceEnabled) {
        try {
            speechRecognition.start();
            showMessage(CONFIG.messages.success.voiceEnabled, 'success');
        } catch (error) {
            console.error('Error starting speech recognition:', error);
            voiceEnabled = false;
        }
    } else {
        speechRecognition.stop();
        showMessage(CONFIG.messages.success.voiceDisabled, 'info');
    }

    updateVoiceToggleButton();
}

// Toggle narration
function toggleNarration() {
    narrationEnabled = !narrationEnabled;

    if (narrationEnabled) {
        showMessage(CONFIG.messages.success.narrationEnabled, 'success');
        speak(CONFIG.messages.success.narrationEnabled);
    } else {
        showMessage(CONFIG.messages.success.narrationDisabled, 'info');
        speechSynthesis.cancel();
    }

    updateNarrationToggleButton();
}

// Update voice toggle button appearance
function updateVoiceToggleButton() {
    const btn = document.getElementById('voiceToggleBtn');
    if (voiceEnabled) {
        btn.innerHTML = '<i class="fas fa-microphone"></i> Disable Voice';
        btn.classList.add('btn-primary');
        btn.classList.remove('btn-outline');
        updateVoiceStatus('Voice commands enabled');
    } else {
        btn.innerHTML = '<i class="fas fa-microphone-slash"></i> Enable Voice';
        btn.classList.remove('btn-primary', 'voice-listening');
        btn.classList.add('btn-outline');
        updateVoiceStatus('Voice commands disabled');
    }
}

// Update narration toggle button appearance
function updateNarrationToggleButton() {
    const btn = document.getElementById('narrateToggleBtn');
    if (narrationEnabled) {
        btn.innerHTML = '<i class="fas fa-volume-up"></i> Disable Narration';
        btn.classList.add('btn-primary');
        btn.classList.remove('btn-outline');
    } else {
        btn.innerHTML = '<i class="fas fa-volume-mute"></i> Enable Narration';
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline');
    }
}

// Update voice status display
function updateVoiceStatus(status) {
    document.getElementById('voiceStatus').textContent = status;
}

// Voice command functions for map navigation
function zoomIn() {
    const currentZoom = map.getZoom();
    map.setZoom(Math.min(currentZoom + 2, CONFIG.map.maxZoom));
    speak('Zooming in');
}

function zoomOut() {
    const currentZoom = map.getZoom();
    map.setZoom(Math.max(currentZoom - 2, CONFIG.map.minZoom));
    speak('Zooming out');
}

function centerMap() {
    if (animationMarker && animationState.isAnimating) {
        map.setCenter(animationMarker.getPosition());
        speak('Centering map on current position');
    } else if (routeData.length > 0) {
        fitMapToBounds();
        speak('Centering map on route');
    } else {
        map.setCenter(CONFIG.map.defaultCenter);
        speak('Centering map');
    }
}

// Voice announcement functions
function announceCurrentLocation() {
    if (animationState.isAnimating && routePath.length > 0) {
        const currentPosition = routePath[animationState.currentStep];
        const nearestPoint = findNearestRoutePoint(currentPosition);
        speak(`Currently near ${nearestPoint.name}`);
    } else if (routeData.length > 0) {
        speak(`Route loaded with ${routeData.length} locations. Ready to start animation.`);
    } else {
        speak('No route data loaded');
    }
}

function announceCurrentSpeed() {
    speak(`Current animation speed is ${animationState.speed} times normal speed`);
}

function announceCurrentProgress() {
    if (animationState.isAnimating || animationState.isPaused) {
        const progress = Math.round((animationState.currentStep / (routePath.length - 1)) * 100);
        speak(`Animation progress is ${progress} percent complete`);
    } else {
        speak('Animation not started');
    }
}

function announceAnimationStatus() {
    if (animationState.isAnimating) {
        speak('Animation is currently running');
    } else if (animationState.isPaused) {
        speak('Animation is paused');
    } else if (routePath.length > 0) {
        speak('Animation is ready to start');
    } else {
        speak('No route data loaded. Please load route data first.');
    }
}

function announceLoadedLocations() {
    if (routeData.length === 0) {
        speak('No locations loaded. Please load route data first.');
        return;
    }

    const locationNames = routeData.map(location => location.name).join(', ');
    const announcement = `${routeData.length} locations loaded and highlighted in red: ${locationNames}`;
    speak(announcement);
}
