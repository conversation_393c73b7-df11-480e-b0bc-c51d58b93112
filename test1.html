<!DOCTYPE html>
<html>
  <head>
    <title>Driver Route Tracker with Voice</title>
    <style>
      #map {
        height: 100vh;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>

    <script>
      let map;
      let markers = [];
      const destinations = [
        { lat: 12.9716, lng: 77.5946, name: "Location 1" }, // Bengaluru
        { lat: 12.9352, lng: 77.6142, name: "Location 2" },
        { lat: 12.9121, lng: 77.6446, name: "Location 3" },
      ];

      function initMap() {
        map = new google.maps.Map(document.getElementById("map"), {
          center: destinations[0],
          zoom: 13,
        });

        destinations.forEach((loc, index) => {
          const marker = new google.maps.Marker({
            position: loc,
            map,
            label: `${index + 1}`,
            title: loc.name,
          });
          markers.push(marker);
        });

        trackDriver();
      }

      function trackDriver() {
        if (navigator.geolocation) {
          navigator.geolocation.watchPosition(
            (position) => {
              const driverPos = {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
              };

              new google.maps.Marker({
                position: driverPos,
                map,
                icon: {
                  path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                  scale: 5,
                  fillColor: "#00f",
                  fillOpacity: 1,
                  strokeWeight: 1,
                },
                title: "Driver Location",
              });

              destinations.forEach((loc, index) => {
                const distance = haversineDistance(driverPos, loc);
                if (distance < 0.2) {
                  speak(`${loc.name} reached`);
                  destinations.splice(index, 1); // Prevent repeat
                }
              });
            },
            (error) => alert("Error getting location: " + error.message),
            { enableHighAccuracy: true }
          );
        } else {
          alert("Geolocation not supported by this browser.");
        }
      }

      function haversineDistance(coord1, coord2) {
        function toRad(x) {
          return (x * Math.PI) / 180;
        }

        const R = 6371; // km
        const dLat = toRad(coord2.lat - coord1.lat);
        const dLon = toRad(coord2.lng - coord1.lng);
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(toRad(coord1.lat)) *
            Math.cos(toRad(coord2.lat)) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
      }

      function speak(text) {
        const msg = new SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(msg);
      }
    </script>

    <script async
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB3JWjSwuSieuFMYXb2gp_XojbaNfIKS6Y&libraries=places&callback=initMap">
    </script>
  </body>
</html>
