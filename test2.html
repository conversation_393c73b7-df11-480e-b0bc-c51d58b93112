<!DOCTYPE html>
<html>
  <head>
    <title>Driver Route Map with Voice</title>
    <style>
      #map {
        height: 100vh;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>

    <script>
      const locations = [
        { lat: 12.9716, lng: 77.5946, label: "Location 1" }, // Bangalore
        { lat: 12.9352, lng: 77.6145, label: "Location 2" }, // Indiranagar
        { lat: 12.9279, lng: 77.6271, label: "Location 3" }  // Koramangala
      ];

      let map, markerIndex = 0, driverMarker;

      function initMap() {
        map = new google.maps.Map(document.getElementById("map"), {
          zoom: 14,
          center: locations[0]
        });

        const bounds = new google.maps.LatLngBounds();
        const markers = [];

        // Add location markers
        locations.forEach((loc, i) => {
          const marker = new google.maps.Marker({
            position: loc,
            map,
            title: loc.label
          });

          markers.push(marker);
          bounds.extend(loc);
        });

        map.fitBounds(bounds);

        // Draw blue route
        const routePath = new google.maps.Polyline({
          path: locations,
          geodesic: true,
          strokeColor: "#0000FF",
          strokeOpacity: 1.0,
          strokeWeight: 4,
        });
        routePath.setMap(map);

        // Add driver marker
        driverMarker = new google.maps.Marker({
          position: locations[0],
          map,
          icon: {
            path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
            scale: 5,
            strokeColor: "#FF0000"
          }
        });

        // Start simulated movement
        simulateMovement();
      }

      function simulateMovement() {
        if (markerIndex >= locations.length) return;

        const target = locations[markerIndex];
        driverMarker.setPosition(target);
        speak(`${target.label} reached`);
        markerIndex++;

        setTimeout(simulateMovement, 5000); // Move every 5 seconds
      }

      function speak(text) {
        const msg = new SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(msg);
      }
    </script>

    <script async
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB3JWjSwuSieuFMYXb2gp_XojbaNfIKS6Y&callback=initMap&libraries=places">
    </script>
  </body>
</html>
