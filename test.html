<!DOCTYPE html>
<html>
<head>
    <title>Delivery Route Map</title>
    <style>
        #map {
            height: 100vh;
            width: 100%;
        }
        .legend {
            background: white;
            padding: 10px;
            font-size: 14px;
        }
    </style>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB3JWjSwuSieuFMYXb2gp_XojbaNfIKS6Y"></script>
</head>
<body>
<div id="map"></div>

<script>
    // Sample customer locations grouped by driver
    const drivers = {
        "Driver 1": [
             {name: "Sree Maha Ganapathi Temple, Kottarakkara",lat: 9.001029451672705,lng: 76.77008521036686},
             {name: "Lulu Mall, Trivandrum",lat: 8.51550002882469,lng: 76.8976382508458},
             {name: "Kollam Railway Station",lat: 8.886214793056155,lng: 76.59508807968544},
             {name: "Elampaloor",lat: 8.955887655784867,lng: 76.67080925269629}
        ]
    };

    const driverColors = {
        "Driver 1": "blue",
    };

    function initMap() {
        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 12,
            center: { lat: 9.001029451672705, lng: 76.77008521036686 }, // Centered in Bangalore
        });

        // Add markers and draw route
        for (const driver in drivers) {
            const pathCoordinates = [];

            drivers[driver].forEach((customer) => {
                const marker = new google.maps.Marker({
                    position: { lat: customer.lat, lng: customer.lng },
                    map: map,
                    title: `${customer.name} (${driver})`,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 18,
                        fillColor: driverColors[driver],
                        fillOpacity: 1,
                        strokeWeight: 1,
                    },
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `<b>${customer.name}</b><br>Assigned to: ${driver}`,
                });

                marker.addListener("click", () => {
                    infoWindow.open(map, marker);
                });

                pathCoordinates.push({ lat: customer.lat, lng: customer.lng });
            });

            // Draw route line
            const routePath = new google.maps.Polyline({
                path: pathCoordinates,
                geodesic: true,
                strokeColor: driverColors[driver],
                strokeOpacity: 7,
                strokeWeight: 15,
            });

            routePath.setMap(map);
        }
    }

    window.onload = initMap;
</script>
</body>
</html>
