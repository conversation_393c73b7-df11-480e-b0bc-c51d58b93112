/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

/* Control Panel */
.control-panel {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.controls-section {
    margin-bottom: 30px;
}

.controls-section:last-child {
    margin-bottom: 0;
}

.controls-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Buttons */
.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 80px;
    justify-content: center;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #4a5568;
    transform: translateY(-1px);
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c53030;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-outline:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

/* Speed Control */
.speed-control {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.speed-control label {
    font-weight: 500;
    color: #4a5568;
}

#speedValue {
    color: #4299e1;
    font-weight: 600;
}

#speedSlider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
}

#speedSlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4299e1;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

#speedSlider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4299e1;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.speed-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #718096;
}

/* Progress */
.progress-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #667eea);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

#progressText {
    font-weight: 600;
    color: #4299e1;
}

#currentLocation {
    color: #718096;
    font-style: italic;
}

/* Map Container */
.map-container {
    position: relative;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: 600px;
}

#map {
    width: 100%;
    height: 100%;
}

/* Map Overlay */
.map-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.legend {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-width: 180px;
}

.legend h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.8rem;
    color: #4a5568;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.marker-icon {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.marker-icon.json-location {
    background: #dc2626;
    width: 16px;
    height: 16px;
    border: 3px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.marker-icon.start {
    background: #48bb78;
}

.marker-icon.waypoint {
    background: #4299e1;
}

.marker-icon.end {
    background: #e53e3e;
}

.marker-icon.animated {
    background: #ed8936;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.route-line {
    width: 20px;
    height: 3px;
    background: #4299e1;
    border-radius: 2px;
}

/* Voice Controls */
.voice-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.voice-controls .btn {
    width: 100%;
}

.voice-status {
    text-align: center;
    font-size: 0.8rem;
    color: #718096;
    font-style: italic;
}

.voice-commands h5 {
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 6px;
    margin-top: 12px;
    color: #4a5568;
}

.voice-commands h5:first-child {
    margin-top: 0;
}

.command-list {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 8px;
}

.command-list span {
    font-size: 0.75rem;
    color: #718096;
    background: #f7fafc;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid #4299e1;
}

.voice-listening {
    animation: voicePulse 1.5s infinite;
}

@keyframes voicePulse {
    0% { background-color: #4299e1; }
    50% { background-color: #e53e3e; }
    100% { background-color: #4299e1; }
}

/* Keyboard Shortcuts */
.shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.85rem;
}

.shortcut-item kbd {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    font-weight: 600;
    color: #4a5568;
    min-width: 40px;
    text-align: center;
}

.shortcut-item span {
    color: #718096;
}

/* Data Section */
.data-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.data-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 8px;
}

.data-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.data-preview h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 15px;
    color: #4a5568;
}

.route-points {
    background: #f7fafc;
    border-radius: 8px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
}

.route-point {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.route-point:last-child {
    border-bottom: none;
}

.route-point-number {
    background: #4299e1;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.route-point-info h5 {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 2px;
}

.route-point-info p {
    font-size: 0.8rem;
    color: #718096;
}

.no-data {
    text-align: center;
    color: #718096;
    font-style: italic;
}

/* Messages */
.message-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.message {
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #48bb78;
}

.message.error {
    background: #e53e3e;
}

.message.info {
    background: #4299e1;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.loading-spinner p {
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .control-panel {
        order: 2;
    }
    
    .map-container {
        order: 1;
        height: 500px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .control-panel {
        padding: 20px;
    }
    
    .button-group {
        justify-content: center;
    }
    
    .btn {
        flex: 1;
        min-width: auto;
    }
    
    .map-container {
        height: 400px;
    }
    
    .map-overlay {
        position: static;
        margin: 15px;
    }
    
    .legend {
        background: white;
        backdrop-filter: none;
    }
    
    .data-controls {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5rem;
    }
    
    .controls-section {
        margin-bottom: 20px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
