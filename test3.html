<!DOCTYPE html>
<html>
  <head>
    <title>Driver Route Tracker with Logs</title>
    <style>
      body, html {
        margin: 0;
        padding: 0;
        height: 100%;
      }
      #map {
        height: 100%;
        width: 75%;
        float: left;
      }
      #log {
        width: 25%;
        height: 100%;
        float: right;
        overflow-y: auto;
        background: #f5f5f5;
        padding: 10px;
        font-family: Arial, sans-serif;
        font-size: 14px;
      }
      .visited {
        background: #d9fdd3;
        padding: 5px;
        margin-bottom: 5px;
        border-left: 4px solid green;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>
    <div id="log"><h3>Visited Locations</h3></div>

    <script>
      let map, driverMarker, directionsService, directionsRenderer;
      const visitedLocations = [];

      const destinationPoints = [
        {name: "Malu",lat: 8.99706788733031,lng: 76.75204008466004},
        {name: "<PERSON>ee Maha Ganapathi Temple, Kottarakkara",lat: 9.001029451672705,lng: 76.77008521036686},
        {name: "Lulu Mall, Trivandrum",lat: 8.51550002882469,lng: 76.8976382508458},
        {name: "Kollam Railway Station",lat: 8.886214793056155,lng: 76.59508807968544},
        {name: "Elampaloor",lat: 8.955887655784867,lng: 76.67080925269629}
      ];

      const locationMarkers = [];
      let currentTargetIndex = 1; // Start after first location

      function initMap() {
        map = new google.maps.Map(document.getElementById("map"), {
          zoom: 15,
          center: destinationPoints[0]
        });

        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({ map });

        // Place all destination markers (default = red)
        destinationPoints.forEach((point, i) => {
          const marker = new google.maps.Marker({
            position: point,
            map,
            title: point.label,
            icon: i === 0 ? "http://maps.google.com/mapfiles/ms/icons/blue-dot.png"
                          : "http://maps.google.com/mapfiles/ms/icons/red-dot.png"
          });
          locationMarkers.push(marker);
        });

        // Driver marker
        driverMarker = new google.maps.Marker({
          map,
          position: destinationPoints[0],
          icon: {
            path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
            scale: 5,
            strokeColor: "#FF0000"
          }
        });

        trackDriver();
      }

      function trackDriver() {
        if (navigator.geolocation) {
          navigator.geolocation.watchPosition(
            (position) => {
              const currentPos = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
              };
              driverMarker.setPosition(currentPos);
              map.setCenter(currentPos);

              if (currentTargetIndex < destinationPoints.length) {
                const target = destinationPoints[currentTargetIndex];

                directionsService.route({
                  origin: currentPos,
                  destination: target,
                  travelMode: google.maps.TravelMode.DRIVING
                }, (result, status) => {
                  if (status === "OK") {
                    directionsRenderer.setDirections(result);

                    const dist = google.maps.geometry.spherical.computeDistanceBetween(
                      new google.maps.LatLng(currentPos),
                      new google.maps.LatLng(target)
                    );

                    if (dist < 50) {
                      // Change marker color to green
                      locationMarkers[currentTargetIndex].setIcon("http://maps.google.com/mapfiles/ms/icons/green-dot.png");

                      // Log visit
                      logVisited(target.label);
                      speak(`${target.label} reached`);

                      currentTargetIndex++;
                    }
                  }
                });
              }
            },
            (error) => {
              alert("Error: " + error.message);
            },
            {
              enableHighAccuracy: true,
              timeout: 5000,
              maximumAge: 0
            }
          );
        } else {
          alert("Geolocation not supported.");
        }
      }

      function speak(text) {
        const msg = new SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(msg);
      }

      function logVisited(label) {
        const logPanel = document.getElementById("log");
        const time = new Date().toLocaleTimeString();
        visitedLocations.push({ label, time });

        const div = document.createElement("div");
        div.className = "visited";
        div.innerHTML = `<strong>${label}</strong><br>Reached at: ${time}`;
        logPanel.appendChild(div);
      }
    </script>

    <script async
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB3JWjSwuSieuFMYXb2gp_XojbaNfIKS6Y&callback=initMap&libraries=geometry">
    </script>
  </body>
</html>
