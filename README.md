# Interactive Route Animation Map

A responsive web application that displays and animates routes between multiple locations using Google Maps JavaScript API. Features smooth animations, interactive controls, and a modern user interface.

## Features

### 🗺️ Map Functionality
- **Google Maps Integration**: Uses Google Maps JavaScript API with DirectionsService for realistic routing
- **Highlighted Locations**: All JSON-specified locations are marked with prominent red markers
- **Dual Marker System**: Red markers for specified locations + colored route markers (start/waypoint/end)
- **Interactive Info Windows**: Click any marker to view detailed location information
- **Multiple Map Views**: Toggle between roadmap and satellite views
- **Responsive Design**: Works seamlessly on desktop and mobile devices

### 🎬 Animation Controls
- **Smooth Animation**: Continuous movement along actual route geometry
- **Play/Pause/Resume**: Full control over animation playback
- **Speed Control**: Adjustable animation speed (0.5x to 3x)
- **Reset Functionality**: Return to starting position
- **Progress Tracking**: Visual progress bar and percentage display

### 🎨 Visual Features
- **Route Visualization**: Clear polyline showing the complete route
- **Completed Path**: Different colored line showing completed portions
- **Moving Marker**: Animated marker following the route path
- **Follow Mode**: Option to keep the map centered on the animated marker
- **Legend**: Clear explanation of all map elements

### 🎤 Voice Features
- **Voice Commands**: Control animation and map with natural speech
- **Text-to-Speech Narration**: Audio feedback for all actions and progress
- **Hands-Free Operation**: Complete control without touching the interface
- **Accessibility Support**: Enhanced experience for visually impaired users
- **Multi-Language Support**: Configurable speech recognition and synthesis

### 📊 Data Management
- **JSON Data Format**: Simple, standardized location data structure
- **Sample Data**: Pre-loaded example route for testing
- **Custom Data Upload**: Load your own route data via JSON file
- **Data Validation**: Automatic validation of coordinate data
- **Route Preview**: Display all route points with descriptions

## Setup Instructions

### 1. Get Google Maps API Key
1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Maps JavaScript API
   - Directions API
   - Geometry Library
4. Create credentials (API Key)
5. Restrict the API key to your domain for security

### 2. Configure the Application
1. Open `index.html`
2. Replace `YOUR_API_KEY` in the Google Maps script tag with your actual API key:
   ```html
   <script async defer 
       src="https://maps.googleapis.com/maps/api/js?key=YOUR_ACTUAL_API_KEY&libraries=geometry&callback=initMap">
   </script>
   ```

### 3. Deploy
1. Upload all files to your web server
2. Ensure your domain is added to the API key restrictions
3. Open `index.html` in a web browser

### 4. Voice Features Setup
1. **Browser Compatibility**: Voice features require modern browsers with Web Speech API support
2. **Microphone Permission**: Allow microphone access when prompted for voice commands
3. **HTTPS Required**: Voice recognition requires HTTPS in production environments
4. **Language Settings**: Configure speech language in `config.js` if needed

## Data Format

The application accepts route data as a JSON array of location objects:

```json
[
  {
    "name": "Sree Maha Ganapathi Temple, Kottarakkara",
    "lat": 9.001029451672705,
    "lng": 76.77008521036686,
    "description": "Sacred Hindu temple dedicated to Lord Ganesha"
  },
  {
    "name": "Lulu Mall, Trivandrum",
    "lat": 8.51550002882469,
    "lng": 76.8976382508458,
    "description": "Popular shopping mall in Thiruvananthapuram"
  }
]
```

### Required Fields
- `name` (string): Display name for the location
- `lat` (number): Latitude coordinate (-90 to 90)
- `lng` (number): Longitude coordinate (-180 to 180)

### Optional Fields
- `description` (string): Additional information about the location

### Validation Rules
- Minimum 2 locations required for a route
- Coordinates must be valid numbers within acceptable ranges
- All required fields must be present

## Marker System

The application uses a dual marker system to clearly distinguish between different types of locations:

### 🔴 Red Markers (JSON-Specified Locations)
- **Purpose**: Highlight all locations specified in your JSON data
- **Appearance**: Large red circles with white borders
- **Info**: Shows "Specified Location #X" with full details
- **Priority**: Highest z-index for maximum visibility

### 🟢🔵🔴 Route Markers (Navigation Points)
- **Green**: Start point of the route
- **Blue**: Waypoints along the route
- **Red**: End point of the route
- **Purpose**: Show the navigation flow and route structure
- **Info**: Shows role (Start/Waypoint/End) with location details

This dual system ensures that:
1. Your original JSON locations are always prominently visible in red
2. The route navigation structure is clearly indicated with colored markers
3. Users can easily distinguish between specified locations and route points
4. All information is accessible through interactive info windows

## Usage Guide

### Loading Data
1. **Default Kerala Route**: Automatically loads on map initialization with 4 Kerala locations
2. **Reload Kerala Route**: Click "Reload Kerala Route" to refresh the default route
3. **Custom Data**: Click "Load Custom Data" to upload your own JSON file

### Animation Controls
1. **Start**: Begin the route animation from the first location
2. **Pause**: Temporarily stop the animation (can be resumed)
3. **Resume**: Continue animation from where it was paused
4. **Reset**: Return to the beginning and stop animation

### Speed Control
- Use the speed slider to adjust animation speed
- Range: 0.5x (slow) to 3x (fast)
- Default: 1x (normal speed)

### Map Controls
- **Fit All**: Zoom and center map to show all route points
- **Follow**: Toggle follow mode to keep map centered on animated marker
- **Satellite/Roadmap**: Switch between map view types

### Voice Controls
- **Enable Voice**: Click to start listening for voice commands
- **Enable Narration**: Toggle text-to-speech audio feedback
- **Voice Commands**: Use natural speech to control the application

#### Animation Voice Commands
- "Start animation" - Begin route animation
- "Pause" - Temporarily stop animation
- "Resume" - Continue from pause point
- "Reset" - Return to beginning
- "Speed up" - Increase animation speed
- "Slow down" - Decrease animation speed

#### Map Voice Commands
- "Fit map" - Zoom to show entire route
- "Follow mode" - Toggle marker tracking
- "Satellite view" - Switch to satellite imagery
- "Roadmap" - Switch to road map view
- "Zoom in" - Increase map zoom level
- "Zoom out" - Decrease map zoom level
- "Center map" - Center on current position or route

#### Information Voice Commands
- "Where am I" - Announce current location
- "What speed" - Announce current animation speed
- "Progress" - Announce completion percentage
- "Status" - Announce animation state

#### Audio Voice Commands
- "Enable narration" - Turn on voice feedback
- "Mute" - Turn off voice feedback

## File Structure

```
├── index.html          # Main HTML page
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality
├── sample-data.json    # Example route data
└── README.md          # This documentation file
```

## Browser Compatibility

- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+
- **Mobile**: iOS Safari 12+, Chrome Mobile 60+

## API Usage and Costs

This application uses the following Google Maps APIs:
- **Maps JavaScript API**: For map display and basic functionality
- **Directions API**: For route calculation between points
- **Geometry Library**: For distance calculations

### Cost Considerations
- Google Maps APIs have usage quotas and pricing
- Monitor your usage in the Google Cloud Console
- Consider implementing usage limits for production applications
- See [Google Maps Platform Pricing](https://cloud.google.com/maps-platform/pricing) for details

## Customization Options

### Styling
- Modify `styles.css` to change colors, fonts, and layout
- Update marker colors in the `createMarkers()` function
- Customize route line appearance in the DirectionsRenderer options

### Animation
- Adjust animation speed and smoothness in the `animate()` function
- Modify the animation frame rate by changing the timeout value
- Customize the animated marker icon and behavior

### Map Configuration
- Change default map center and zoom in `initMap()`
- Add custom map styles for different visual themes
- Modify info window content and styling

## Troubleshooting

### Common Issues

1. **Map not loading**
   - Check that your API key is correct and properly configured
   - Ensure the Maps JavaScript API is enabled
   - Verify domain restrictions on your API key

2. **Route calculation fails**
   - Check that the Directions API is enabled
   - Verify coordinates are valid and reachable by road
   - Check API quotas and billing in Google Cloud Console

3. **Animation not smooth**
   - Reduce animation speed for better performance
   - Check browser performance and close other tabs
   - Ensure stable internet connection for map tiles

4. **Mobile display issues**
   - Test responsive design on actual devices
   - Check viewport meta tag is present
   - Verify touch interactions work properly

### Error Messages
The application provides user-friendly error messages for:
- Invalid JSON data format
- API authentication failures
- Route calculation errors
- File upload issues

## Contributing

To contribute to this project:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on multiple devices
5. Submit a pull request with detailed description

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
1. Check this README for common solutions
2. Review the browser console for error messages
3. Verify Google Maps API configuration
4. Test with the provided sample data first
