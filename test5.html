<!DOCTYPE html>
<html>
  <head>
    <title>Driver Tracker - Logs + ETA</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        font-family: Arial, sans-serif;
      }
      #map {
        height: 100%;
        width: 75%;
        float: left;
      }
      #log {
        width: 25%;
        height: 100%;
        float: right;
        overflow-y: auto;
        background: #f9f9f9;
        padding: 10px;
        box-sizing: border-box;
      }
      .visited {
        background: #d4edda;
        border-left: 4px solid green;
        padding: 6px;
        margin-bottom: 5px;
      }
      button {
        width: 100%;
        padding: 10px;
        margin-bottom: 10px;
        font-weight: bold;
        cursor: pointer;
      }
      .eta-box {
        background: #fff3cd;
        border-left: 4px solid #ffcc00;
        padding: 6px;
        margin-bottom: 10px;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div id="map"></div>
    <div id="log">
      <button onclick="toggleMode()">🔄 Toggle Mode (Now: <span id='mode'>Live GPS</span>)</button>
      <button onclick="resetMap()">🔁 Reset Map</button>
      <button id="downloadBtn" onclick="downloadCSV()" style="display:none;">📁 Download Logs</button>

      <div id="etaPanel" class="eta-box">ETA: —<br>Distance: —</div>

      <h3>Visited Locations</h3>
      <div id="visitedList"></div>
    </div>

    <script>
      let map, driverMarker, directionsService, directionsRenderer;
      const visitedLocations = [];
      const visitedDiv = document.getElementById("visitedList");
      const modeLabel = document.getElementById("mode");
      const etaPanel = document.getElementById("etaPanel");

      const destinationPoints = [
       {label: "Malu",lat: 8.997002556332896,lng: 76.75208299999998}, 
        {label: "Sree Maha Ganapathi Temple, Kottarakkara",lat: 9.001029451672705,lng: 76.77008521036686},
        {label: "Lulu Mall, Trivandrum",lat: 8.51550002882469,lng: 76.8976382508458},
        {label: "Kollam Railway Station",lat: 8.886214793056155,lng: 76.59508807968544},
        {label: "Elampaloor",lat: 8.955887655784867,lng: 76.67080925269629}
      ];

      const locationMarkers = [];
      let currentTargetIndex = 1;
      let useSimulate = false;
      let simulateInterval = null;
      let watchId = null;

      function initMap() {
        map = new google.maps.Map(document.getElementById("map"), {
          zoom: 15,
          center: destinationPoints[0]
        });

        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({ map });

        destinationPoints.forEach((point, i) => {
          const marker = new google.maps.Marker({
            position: point,
            map,
            title: point.label,
            icon: i === 0
              ? "http://maps.google.com/mapfiles/ms/icons/blue-dot.png"
              : "http://maps.google.com/mapfiles/ms/icons/red-dot.png"
          });
          locationMarkers.push(marker);
        });

        driverMarker = new google.maps.Marker({
          map,
          position: destinationPoints[0],
          icon: {
            path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
            scale: 5,
            strokeColor: "#FF0000"
          }
        });

        startTracking();
      }

      function startTracking() {
        if (useSimulate) {
          startSimulated();
        } else {
          startLiveGPS();
        }
      }

      function startLiveGPS() {
        if (!navigator.geolocation) {
          alert("Geolocation not supported.");
          return;
        }

        watchId = navigator.geolocation.watchPosition(
          (position) => {
            const currentPos = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };

            console.log("Live GPS:", currentPos);
            updateDriver(currentPos);
          },
          (error) => {
            console.error("GPS error:", error);
            alert("Enable location permissions.");
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          }
        );
      }

      function startSimulated() {
        let index = 0;
        simulateInterval = setInterval(() => {
          if (index >= destinationPoints.length) {
            clearInterval(simulateInterval);
            return;
          }
          const pos = destinationPoints[index];
          updateDriver(pos);
          index++;
        }, 5000);
      }

      function updateDriver(currentPos) {
        driverMarker.setPosition(currentPos);
        map.panTo(currentPos);

        if (currentTargetIndex < destinationPoints.length) {
          const target = destinationPoints[currentTargetIndex];

          directionsService.route({
            origin: currentPos,
            destination: target,
            travelMode: google.maps.TravelMode.DRIVING
          }, (result, status) => {
            if (status === "OK") {
              directionsRenderer.setDirections(result);

              const routeLeg = result.routes[0].legs[0];
              const distance = routeLeg.distance.text;
              const duration = routeLeg.duration.text;

              // 🔧 Update ETA and distance UI
              etaPanel.innerHTML = `Next: ${target.label}<br>ETA: ${duration}<br>Distance: ${distance}`;

              const distVal = routeLeg.distance.value; // in meters
              if (distVal < 50) {
                locationMarkers[currentTargetIndex].setIcon("http://maps.google.com/mapfiles/ms/icons/green-dot.png");
                logVisited(target.label);
                speak(`${target.label} reached`);
                currentTargetIndex++;

                if (currentTargetIndex >= destinationPoints.length) {
                  etaPanel.innerHTML = "✅ All locations visited";
                }
              }
            }
          });
        } else {
          etaPanel.innerHTML = "✅ All locations visited";
        }
      }

      function speak(text) {
        const msg = new SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(msg);
      }

      function logVisited(label) {
        const time = new Date().toLocaleTimeString();
        visitedLocations.push({ label, time });

        const div = document.createElement("div");
        div.className = "visited";
        div.innerHTML = `<strong>${label}</strong><br>Reached at: ${time}`;
        visitedDiv.appendChild(div);

        // 🔧 Show download button
        document.getElementById("downloadBtn").style.display = "block";
      }

      function toggleMode() {
        resetTracking();
        useSimulate = !useSimulate;
        modeLabel.textContent = useSimulate ? "Simulated" : "Live GPS";
        startTracking();
      }

      function resetMap() {
        resetTracking();
        currentTargetIndex = 1;
        visitedLocations.length = 0;
        visitedDiv.innerHTML = "";
        etaPanel.innerHTML = "ETA: —<br>Distance: —";
        modeLabel.textContent = useSimulate ? "Simulated" : "Live GPS";
        document.getElementById("downloadBtn").style.display = "none";

        locationMarkers.forEach((marker, i) => {
          const icon = i === 0
            ? "http://maps.google.com/mapfiles/ms/icons/blue-dot.png"
            : "http://maps.google.com/mapfiles/ms/icons/red-dot.png";
          marker.setIcon(icon);
        });

        driverMarker.setPosition(destinationPoints[0]);
        map.panTo(destinationPoints[0]);
        startTracking();
      }

      function resetTracking() {
        if (watchId !== null) {
          navigator.geolocation.clearWatch(watchId);
          watchId = null;
        }
        if (simulateInterval !== null) {
          clearInterval(simulateInterval);
          simulateInterval = null;
        }
      }

      // 🔧 CSV Download
      function downloadCSV() {
        if (visitedLocations.length === 0) return;

        let csv = "Label,Time\n";
        visitedLocations.forEach(item => {
          csv += `"${item.label}","${item.time}"\n`;
        });

        const blob = new Blob([csv], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "visited_locations.csv";
        a.click();
        URL.revokeObjectURL(url);
      }
    </script>

    <script async
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB3JWjSwuSieuFMYXb2gp_XojbaNfIKS6Y&callback=initMap&libraries=geometry">
    </script>
  </body>
</html>
