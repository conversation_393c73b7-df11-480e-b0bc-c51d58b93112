<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Commands Demo - Interactive Route Animation</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .demo-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-section h2 {
            color: #ffd700;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .command-category {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .command-category h3 {
            color: #87ceeb;
            margin-bottom: 15px;
        }
        
        .command-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .command-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        
        .setup-steps {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
        }
        
        .setup-steps ol {
            padding-left: 20px;
        }
        
        .setup-steps li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .demo-button {
            display: inline-block;
            background: #4299e1;
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        .note {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .note strong {
            color: #ffc107;
        }
        
        @media (max-width: 768px) {
            .command-list {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎤 Voice Commands Demo</h1>
        
        <div class="demo-section">
            <h2>🚀 Quick Start</h2>
            <div class="setup-steps">
                <ol>
                    <li><strong>Open the main application:</strong> <a href="index.html" class="demo-button">Launch Interactive Map</a></li>
                    <li><strong>Kerala locations load automatically</strong> - no need to click any buttons!</li>
                    <li><strong>Notice the red markers</strong> highlighting all Kerala locations on the map</li>
                    <li><strong>Allow microphone access</strong> when prompted by your browser</li>
                    <li><strong>Click "Enable Voice"</strong> in the Voice Controls section</li>
                    <li><strong>Try saying:</strong> "Start animation" to begin the Kerala tour</li>
                    <li><strong>Say "List locations"</strong> to hear all the highlighted Kerala locations</li>
                </ol>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 Available Voice Commands</h2>
            
            <div class="command-category">
                <h3>🎬 Animation Control</h3>
                <div class="command-list">
                    <div class="command-item">"Start animation"</div>
                    <div class="command-item">"Begin"</div>
                    <div class="command-item">"Play"</div>
                    <div class="command-item">"Pause"</div>
                    <div class="command-item">"Stop"</div>
                    <div class="command-item">"Resume"</div>
                    <div class="command-item">"Continue"</div>
                    <div class="command-item">"Reset"</div>
                    <div class="command-item">"Restart"</div>
                </div>
            </div>

            <div class="command-category">
                <h3>⚡ Speed Control</h3>
                <div class="command-list">
                    <div class="command-item">"Speed up"</div>
                    <div class="command-item">"Faster"</div>
                    <div class="command-item">"Slow down"</div>
                    <div class="command-item">"Slower"</div>
                </div>
            </div>

            <div class="command-category">
                <h3>🗺️ Map Navigation</h3>
                <div class="command-list">
                    <div class="command-item">"Fit map"</div>
                    <div class="command-item">"Fit all"</div>
                    <div class="command-item">"Zoom out"</div>
                    <div class="command-item">"Follow mode"</div>
                    <div class="command-item">"Follow"</div>
                    <div class="command-item">"Track"</div>
                    <div class="command-item">"Satellite view"</div>
                    <div class="command-item">"Satellite"</div>
                    <div class="command-item">"Roadmap"</div>
                    <div class="command-item">"Map view"</div>
                    <div class="command-item">"Zoom in"</div>
                    <div class="command-item">"Center map"</div>
                </div>
            </div>

            <div class="command-category">
                <h3>📍 Information & Status</h3>
                <div class="command-list">
                    <div class="command-item">"Where am I"</div>
                    <div class="command-item">"Current location"</div>
                    <div class="command-item">"How fast"</div>
                    <div class="command-item">"What speed"</div>
                    <div class="command-item">"Progress"</div>
                    <div class="command-item">"Status"</div>
                    <div class="command-item">"List locations"</div>
                    <div class="command-item">"Show locations"</div>
                    <div class="command-item">"What locations"</div>
                </div>
            </div>

            <div class="command-category">
                <h3>🔊 Audio Control</h3>
                <div class="command-list">
                    <div class="command-item">"Enable narration"</div>
                    <div class="command-item">"Unmute"</div>
                    <div class="command-item">"Disable narration"</div>
                    <div class="command-item">"Mute"</div>
                </div>
            </div>

            <div class="command-category">
                <h3>📊 Data Management</h3>
                <div class="command-list">
                    <div class="command-item">"Load sample"</div>
                    <div class="command-item">"Sample data"</div>
                    <div class="command-item">"Reload Kerala"</div>
                    <div class="command-item">"Reload route"</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>💡 Tips for Best Results</h2>
            <div class="setup-steps">
                <ul>
                    <li><strong>Speak clearly</strong> and at a normal pace</li>
                    <li><strong>Use exact phrases</strong> from the command list above</li>
                    <li><strong>Wait for confirmation</strong> before giving the next command</li>
                    <li><strong>Enable narration</strong> to hear audio feedback for all actions</li>
                    <li><strong>Use a quiet environment</strong> for better recognition accuracy</li>
                    <li><strong>Try alternative phrases</strong> if a command isn't recognized</li>
                </ul>
            </div>
        </div>

        <div class="note">
            <strong>Note:</strong> Voice recognition requires a modern browser with Web Speech API support (Chrome, Edge, Safari) and microphone permissions. HTTPS is required for voice features in production environments.
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="demo-button">🚀 Try Voice Commands Now</a>
            <a href="README.md" class="demo-button">📖 Read Full Documentation</a>
        </div>
    </div>
</body>
</html>
