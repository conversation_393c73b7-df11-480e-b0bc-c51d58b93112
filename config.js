// Configuration file for the Interactive Route Animation Map
// Modify these settings to customize the application behavior

const CONFIG = {
    // Map settings
    map: {
        // Default center of the map (Kerala, India)
        defaultCenter: { lat: 8.7642, lng: 76.7729 },
        defaultZoom: 10,
        maxZoom: 18,
        minZoom: 3,
        
        // Map styling
        styles: [
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }]
            }
        ]
    },
    
    // Animation settings
    animation: {
        // Default animation speed (1x)
        defaultSpeed: 1,
        
        // Speed range for the slider
        minSpeed: 0.5,
        maxSpeed: 3,
        speedStep: 0.5,
        
        // Animation frame rate (milliseconds between frames)
        frameRate: 50,
        
        // Follow mode zoom level
        followZoom: 12
    },
    
    // Route visualization
    route: {
        // Main route line styling
        mainRoute: {
            strokeColor: '#4299e1',
            strokeWeight: 4,
            strokeOpacity: 0.8
        },
        
        // Completed route line styling
        completedRoute: {
            strokeColor: '#48bb78',
            strokeWeight: 6,
            strokeOpacity: 0.9,
            zIndex: 100
        }
    },
    
    // Marker settings
    markers: {
        // JSON specified locations (highlighted in red)
        jsonLocation: {
            fillColor: '#dc2626',
            scale: 12,
            strokeColor: '#ffffff',
            strokeWeight: 3,
            zIndex: 500
        },

        // Start point marker
        start: {
            fillColor: '#48bb78',
            scale: 10,
            strokeColor: '#ffffff',
            strokeWeight: 3,
            zIndex: 400
        },

        // Waypoint marker
        waypoint: {
            fillColor: '#4299e1',
            scale: 10,
            strokeColor: '#ffffff',
            strokeWeight: 3,
            zIndex: 400
        },

        // End point marker
        end: {
            fillColor: '#e53e3e',
            scale: 10,
            strokeColor: '#ffffff',
            strokeWeight: 3,
            zIndex: 400
        },

        // Animated marker
        animated: {
            fillColor: '#ed8936',
            scale: 12,
            strokeColor: '#ffffff',
            strokeWeight: 3,
            zIndex: 1000
        }
    },
    
    // Voice settings
    voice: {
        // Text-to-speech settings
        speech: {
            enabled: false,
            rate: 1.0,
            pitch: 1.0,
            volume: 0.8,
            lang: 'en-US'
        },

        // Voice recognition settings
        recognition: {
            enabled: false,
            continuous: true,
            interimResults: false,
            lang: 'en-US',
            maxAlternatives: 1
        },

        // Voice commands mapping
        commands: {
            'start animation': 'startAnimation',
            'start': 'startAnimation',
            'begin': 'startAnimation',
            'play': 'startAnimation',
            'pause': 'pauseAnimation',
            'stop': 'pauseAnimation',
            'resume': 'resumeAnimation',
            'continue': 'resumeAnimation',
            'reset': 'resetAnimation',
            'restart': 'resetAnimation',
            'fit map': 'fitMapToBounds',
            'fit all': 'fitMapToBounds',
            'zoom out': 'fitMapToBounds',
            'follow mode': 'toggleFollowMode',
            'follow': 'toggleFollowMode',
            'track': 'toggleFollowMode',
            'satellite view': 'toggleMapType',
            'satellite': 'toggleMapType',
            'roadmap': 'toggleMapType',
            'map view': 'toggleMapType',
            'speed up': 'increaseSpeed',
            'faster': 'increaseSpeed',
            'slow down': 'decreaseSpeed',
            'slower': 'decreaseSpeed',
            'load sample': 'loadSampleData',
            'sample data': 'loadSampleData',
            'reload kerala': 'loadSampleData',
            'reload route': 'loadSampleData',
            'zoom in': 'zoomIn',
            'zoom out': 'zoomOut',
            'center map': 'centerMap',
            'show route': 'fitMapToBounds',
            'enable narration': 'enableNarration',
            'disable narration': 'disableNarration',
            'mute': 'disableNarration',
            'unmute': 'enableNarration',
            'where am i': 'announceLocation',
            'current location': 'announceLocation',
            'how fast': 'announceSpeed',
            'what speed': 'announceSpeed',
            'progress': 'announceProgress',
            'status': 'announceStatus',
            'list locations': 'announceLocations',
            'show locations': 'announceLocations',
            'what locations': 'announceLocations'
        }
    },

    // UI settings
    ui: {
        // Message display duration (milliseconds)
        messageDisplayTime: 5000,

        // Loading overlay settings
        loadingMessages: {
            default: 'Loading...',
            mapInit: 'Loading map and calculating route...',
            routeCalc: 'Calculating route...',
            sampleData: 'Loading Kerala route data...',
            customData: 'Loading custom route data...',
            autoLoad: 'Loading Kerala locations...'
        }
    },
    
    // Data validation
    validation: {
        // Minimum number of points required for a route
        minRoutePoints: 2,
        
        // Maximum number of points (to prevent performance issues)
        maxRoutePoints: 25,
        
        // Coordinate bounds
        latBounds: { min: -90, max: 90 },
        lngBounds: { min: -180, max: 180 }
    },
    
    // Error messages
    messages: {
        errors: {
            apiKey: 'Google Maps API authentication failed. Please check your API key.',
            routeCalc: 'Error calculating route. Please check your locations.',
            invalidData: 'Invalid route data format. Please check the documentation.',
            fileFormat: 'Please select a valid JSON file.',
            minPoints: 'At least 2 points are required for a route.',
            maxPoints: 'Maximum 25 points allowed for optimal performance.',
            invalidCoords: 'Invalid coordinates detected. Please check your data.',
            networkError: 'Network error. Please check your connection.',
            loadData: 'Please load route data first.',
            voiceNotSupported: 'Voice recognition is not supported in this browser.',
            speechNotSupported: 'Text-to-speech is not supported in this browser.',
            microphonePermission: 'Microphone permission denied. Please allow microphone access.',
            voiceCommandNotRecognized: 'Voice command not recognized. Try again.'
        },
        
        success: {
            mapInit: 'Map initialized successfully! Load route data to get started.',
            routeCalc: 'Route calculated successfully!',
            dataLoaded: 'Route data loaded successfully!',
            animationComplete: 'Route animation completed!',
            connectionRestored: 'Connection restored',
            voiceEnabled: 'Voice commands enabled. Try saying "start animation"',
            voiceDisabled: 'Voice commands disabled',
            narrationEnabled: 'Voice narration enabled',
            narrationDisabled: 'Voice narration disabled'
        },
        
        info: {
            connectionLost: 'Connection lost. Some features may not work properly.',
            followMode: 'Follow mode enabled - map will track animation',
            followModeOff: 'Follow mode disabled',
            voiceListening: 'Listening for voice commands...',
            voiceProcessing: 'Processing voice command...'
        }
    },

    // Voice narration text
    narration: {
        animationStart: 'Starting route animation from {start} to {end}',
        animationPause: 'Animation paused',
        animationResume: 'Animation resumed',
        animationReset: 'Animation reset to beginning',
        animationComplete: 'Route animation completed. Journey from {start} to {end} finished.',
        locationUpdate: 'Now approaching {location}',
        speedChange: 'Animation speed changed to {speed}',
        followModeOn: 'Follow mode enabled. Map will track the animated marker.',
        followModeOff: 'Follow mode disabled.',
        mapTypeChange: 'Map view changed to {type}',
        routeLoaded: 'Route loaded with {count} locations highlighted in red. Ready to start animation.',
        progressUpdate: 'Animation progress: {percent} percent complete'
    }
};

// Export configuration for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
