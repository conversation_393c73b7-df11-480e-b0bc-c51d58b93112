// Metronic Dashboard JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {

    // Initialize Metronic components
    initializeMetronicComponents();

    // Initialize tooltips and popovers
    initializeBootstrapComponents();

    // Animate statistics cards on load
    animateStatCards();

    // Add click handlers for interactive elements
    initializeClickHandlers();

    // Initialize sidebar functionality
    initializeSidebar();

    // Auto-refresh data every 30 seconds (optional)
    // setInterval(refreshDashboardData, 30000);
});

// Initialize Metronic specific components
function initializeMetronicComponents() {
    // Initialize sidebar toggle
    const sidebarToggle = document.getElementById('kt_app_sidebar_toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('app-sidebar-minimize');
            localStorage.setItem('sidebar-minimize', document.body.classList.contains('app-sidebar-minimize'));
        });
    }

    // Initialize mobile sidebar toggle
    const mobileSidebarToggle = document.getElementById('kt_app_sidebar_mobile_toggle');
    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('app-sidebar-on');
        });
    }

    // Restore sidebar state
    if (localStorage.getItem('sidebar-minimize') === 'true') {
        document.body.classList.add('app-sidebar-minimize');
    }
}

// Initialize Bootstrap components
function initializeBootstrapComponents() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Animate statistics cards
function animateStatCards() {
    const statCards = document.querySelectorAll('.card-flush .fs-4');

    statCards.forEach((card, index) => {
        const text = card.textContent.trim();
        let finalValue;
        let isPercentage = false;
        let isDollar = false;

        // Check if it's a percentage
        if (text.includes('%')) {
            finalValue = parseFloat(text.replace('%', ''));
            isPercentage = true;
        }
        // Check if it's a dollar amount
        else if (text.includes('$')) {
            finalValue = parseFloat(text.replace(/[$,]/g, ''));
            isDollar = true;
        }
        // Regular number
        else {
            finalValue = parseInt(text.replace(/,/g, ''));
        }

        if (isNaN(finalValue)) return;

        const duration = 2000; // 2 seconds
        const startTime = Date.now() + (index * 300); // Stagger animations

        function updateValue() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            if (progress >= 0) {
                const currentValue = finalValue * easeOutQuart(progress);

                if (isPercentage) {
                    card.textContent = currentValue.toFixed(2) + '%';
                } else if (isDollar) {
                    card.textContent = '$' + currentValue.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                } else {
                    card.textContent = Math.floor(currentValue).toLocaleString();
                }

                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                }
            } else {
                requestAnimationFrame(updateValue);
            }
        }

        // Reset to 0 initially
        if (isPercentage) {
            card.textContent = '0.00%';
        } else if (isDollar) {
            card.textContent = '$0.00';
        } else {
            card.textContent = '0';
        }

        requestAnimationFrame(updateValue);
    });
}

// Easing function for smooth animation
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

// Initialize sidebar functionality
function initializeSidebar() {
    // Menu item click handlers
    const menuLinks = document.querySelectorAll('.menu-link');
    menuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't prevent default for actual links
            if (this.getAttribute('href') === '#') {
                e.preventDefault();
            }

            // Remove active class from all links
            menuLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');

            // Store active menu item
            localStorage.setItem('active-menu', this.querySelector('.menu-title')?.textContent || '');
        });
    });

    // Restore active menu item
    const activeMenu = localStorage.getItem('active-menu');
    if (activeMenu) {
        menuLinks.forEach(link => {
            if (link.querySelector('.menu-title')?.textContent === activeMenu) {
                link.classList.add('active');
            }
        });
    }
}

// Initialize click handlers
function initializeClickHandlers() {
    
    // Table action buttons
    const actionButtons = document.querySelectorAll('.table .btn-icon');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const icon = this.querySelector('i');
            let action = 'unknown';

            if (icon.classList.contains('fa-eye')) action = 'view';
            else if (icon.classList.contains('fa-edit')) action = 'edit';
            else if (icon.classList.contains('fa-trash')) action = 'delete';

            handleTableAction(action, this.closest('tr'));
        });
    });

    // Export buttons
    const exportBtns = document.querySelectorAll('.btn-light-primary');
    exportBtns.forEach(btn => {
        if (btn.textContent.includes('Export')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showNotification('Export functionality would be implemented here', 'info');
            });
        }
    });

    // Add New buttons
    const addNewBtns = document.querySelectorAll('.btn-primary');
    addNewBtns.forEach(btn => {
        if (btn.textContent.includes('Add')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showNotification('Add new functionality would be implemented here', 'info');
            });
        }
    });

    // Chart period buttons
    const chartPeriodBtns = document.querySelectorAll('input[name="chart_period"]');
    chartPeriodBtns.forEach(btn => {
        btn.addEventListener('change', function() {
            showNotification(`Chart period changed to: ${this.value}`, 'success');
            // Here you would typically reload chart data
        });
    });
}

// Handle table actions
function handleTableAction(action, row) {
    const productName = row.cells[0].textContent;
    
    switch(action) {
        case 'view':
            showNotification(`Viewing details for: ${productName}`, 'info');
            break;
        case 'edit':
            showNotification(`Editing: ${productName}`, 'warning');
            break;
        case 'delete':
            if (confirm(`Are you sure you want to delete: ${productName}?`)) {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    showNotification(`${productName} has been deleted`, 'success');
                }, 300);
            }
            break;
    }
}

// Show notification with Metronic styling
function showNotification(message, type = 'info') {
    // Map types to Metronic colors
    const typeMap = {
        'info': 'primary',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger',
        'danger': 'danger'
    };

    const metronicType = typeMap[type] || 'primary';

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${metronicType} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 90px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        max-width: 400px;
        border-radius: 0.75rem;
        box-shadow: 0 0 20px 0 rgba(76, 87, 125, 0.15);
        border: none;
        font-weight: 500;
    `;

    // Icon mapping
    const iconMap = {
        'primary': 'info-circle',
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'danger': 'times-circle'
    };

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${iconMap[metronicType]} fs-2 me-3"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 150);
        }
    }, 5000);
}

// Refresh dashboard data (placeholder function)
function refreshDashboardData() {
    // This would typically make an AJAX call to get updated data
    console.log('Refreshing dashboard data...');
    
    // Example: Update a random statistic
    const statCards = document.querySelectorAll('.card .h5');
    if (statCards.length > 0) {
        const randomCard = statCards[Math.floor(Math.random() * statCards.length)];
        const currentValue = parseInt(randomCard.textContent.replace(/,/g, ''));
        const newValue = currentValue + Math.floor(Math.random() * 10) - 5; // Random change
        
        if (newValue > 0) {
            randomCard.textContent = newValue.toLocaleString();
            
            // Add a subtle flash effect
            randomCard.style.transition = 'background-color 0.3s ease';
            randomCard.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
            setTimeout(() => {
                randomCard.style.backgroundColor = '';
            }, 300);
        }
    }
}

// Responsive sidebar toggle for mobile
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

// Add smooth scrolling to anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading state to buttons
function addLoadingState(button, text = 'Loading...') {
    const originalText = button.innerHTML;
    button.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>${text}`;
    button.disabled = true;
    
    return function removeLoadingState() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// Format numbers with animation
function animateNumber(element, start, end, duration = 1000) {
    const startTime = Date.now();
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * easeOutQuart(progress));
        element.textContent = current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// Initialize charts with responsive options
function initializeCharts() {
    Chart.defaults.responsive = true;
    Chart.defaults.maintainAspectRatio = false;
    Chart.defaults.plugins.legend.display = true;
    Chart.defaults.plugins.legend.position = 'top';
}

// Call chart initialization
initializeCharts();
