// Dashboard JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animate statistics cards on load
    animateStatCards();
    
    // Add click handlers for interactive elements
    initializeClickHandlers();
    
    // Auto-refresh data every 30 seconds (optional)
    // setInterval(refreshDashboardData, 30000);
});

// Animate statistics cards
function animateStatCards() {
    const statCards = document.querySelectorAll('.card .h5');
    
    statCards.forEach((card, index) => {
        const finalValue = parseInt(card.textContent.replace(/,/g, ''));
        const duration = 2000; // 2 seconds
        const startTime = Date.now() + (index * 200); // Stagger animations
        
        function updateValue() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            if (progress >= 0) {
                const currentValue = Math.floor(finalValue * easeOutQuart(progress));
                card.textContent = currentValue.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                }
            } else {
                requestAnimationFrame(updateValue);
            }
        }
        
        // Reset to 0 initially
        card.textContent = '0';
        requestAnimationFrame(updateValue);
    });
}

// Easing function for smooth animation
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

// Initialize click handlers
function initializeClickHandlers() {
    // Sidebar navigation
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all links
            sidebarLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');
        });
    });
    
    // Table action buttons
    const actionButtons = document.querySelectorAll('.table .btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.querySelector('i').classList.contains('fa-eye') ? 'view' :
                          this.querySelector('i').classList.contains('fa-edit') ? 'edit' : 'delete';
            
            handleTableAction(action, this.closest('tr'));
        });
    });
    
    // Export button
    const exportBtn = document.querySelector('.btn-outline-secondary');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            showNotification('Export functionality would be implemented here', 'info');
        });
    }
    
    // Add New button
    const addNewBtn = document.querySelector('.btn-primary');
    if (addNewBtn && addNewBtn.textContent.includes('Add New')) {
        addNewBtn.addEventListener('click', function() {
            showNotification('Add new functionality would be implemented here', 'info');
        });
    }
}

// Handle table actions
function handleTableAction(action, row) {
    const productName = row.cells[0].textContent;
    
    switch(action) {
        case 'view':
            showNotification(`Viewing details for: ${productName}`, 'info');
            break;
        case 'edit':
            showNotification(`Editing: ${productName}`, 'warning');
            break;
        case 'delete':
            if (confirm(`Are you sure you want to delete: ${productName}?`)) {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    showNotification(`${productName} has been deleted`, 'success');
                }, 300);
            }
            break;
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 80px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 150);
        }
    }, 5000);
}

// Refresh dashboard data (placeholder function)
function refreshDashboardData() {
    // This would typically make an AJAX call to get updated data
    console.log('Refreshing dashboard data...');
    
    // Example: Update a random statistic
    const statCards = document.querySelectorAll('.card .h5');
    if (statCards.length > 0) {
        const randomCard = statCards[Math.floor(Math.random() * statCards.length)];
        const currentValue = parseInt(randomCard.textContent.replace(/,/g, ''));
        const newValue = currentValue + Math.floor(Math.random() * 10) - 5; // Random change
        
        if (newValue > 0) {
            randomCard.textContent = newValue.toLocaleString();
            
            // Add a subtle flash effect
            randomCard.style.transition = 'background-color 0.3s ease';
            randomCard.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
            setTimeout(() => {
                randomCard.style.backgroundColor = '';
            }, 300);
        }
    }
}

// Responsive sidebar toggle for mobile
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

// Add smooth scrolling to anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading state to buttons
function addLoadingState(button, text = 'Loading...') {
    const originalText = button.innerHTML;
    button.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>${text}`;
    button.disabled = true;
    
    return function removeLoadingState() {
        button.innerHTML = originalText;
        button.disabled = false;
    };
}

// Format numbers with animation
function animateNumber(element, start, end, duration = 1000) {
    const startTime = Date.now();
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * easeOutQuart(progress));
        element.textContent = current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

// Initialize charts with responsive options
function initializeCharts() {
    Chart.defaults.responsive = true;
    Chart.defaults.maintainAspectRatio = false;
    Chart.defaults.plugins.legend.display = true;
    Chart.defaults.plugins.legend.position = 'top';
}

// Call chart initialization
initializeCharts();
