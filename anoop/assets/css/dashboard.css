/* Metronic Dashboard Styles */

/* Root Variables */
:root {
    --kt-primary: #3699FF;
    --kt-primary-light: rgba(54, 153, 255, 0.1);
    --kt-success: #1BC5BD;
    --kt-success-light: rgba(27, 197, 189, 0.1);
    --kt-info: #8950FC;
    --kt-info-light: rgba(137, 80, 252, 0.1);
    --kt-warning: #FFA800;
    --kt-warning-light: rgba(255, 168, 0, 0.1);
    --kt-danger: #F64E60;
    --kt-danger-light: rgba(246, 78, 96, 0.1);
    --kt-dark: #181C32;
    --kt-light: #F5F8FA;
    --kt-gray-100: #F3F6F9;
    --kt-gray-200: #EBEDF3;
    --kt-gray-300: #E4E6EA;
    --kt-gray-400: #B5B5C3;
    --kt-gray-500: #A1A5B7;
    --kt-gray-600: #7E8299;
    --kt-gray-700: #5E6278;
    --kt-gray-800: #3F4254;
    --kt-gray-900: #181C32;
    --kt-white: #ffffff;
}

/* Body and Layout */
.metronic-body {
    font-family: 'Inter', sans-serif;
    font-size: 13px;
    line-height: 1.5;
    color: var(--kt-gray-700);
    background-color: var(--kt-gray-100);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* App Layout */
.app-root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.app-page {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Header Styles */
.app-header {
    background-color: var(--kt-white);
    border-bottom: 1px solid var(--kt-gray-200);
    box-shadow: 0 0 20px 0 rgba(76, 87, 125, 0.02);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
    display: flex;
    align-items: center;
}

.app-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.page-heading {
    margin: 0;
    font-weight: 600;
    font-size: 1.35rem;
    color: var(--kt-gray-900);
}

.page-desc {
    font-size: 0.85rem;
    color: var(--kt-gray-500);
    font-weight: 500;
}

/* Sidebar Styles */
.app-sidebar {
    position: fixed;
    top: 70px;
    left: 0;
    bottom: 0;
    width: 265px;
    background-color: var(--kt-dark);
    z-index: 999;
    transition: all 0.3s ease;
    overflow: hidden;
}

.app-sidebar-logo {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.app-sidebar-toggle {
    display: none;
}

.app-sidebar-menu {
    height: calc(100vh - 140px);
    overflow-y: auto;
}

.app-sidebar-wrapper {
    padding: 1rem 0;
}

/* Menu Styles */
.menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-item {
    margin: 0;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0.475rem;
    margin: 0.125rem 1rem;
    font-weight: 500;
    font-size: 0.925rem;
}

.menu-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--kt-white);
    transform: translateX(5px);
}

.menu-link.active {
    background-color: var(--kt-primary);
    color: var(--kt-white);
    box-shadow: 0 0 20px 0 rgba(54, 153, 255, 0.3);
}

.menu-icon {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
    font-size: 1.1rem;
}

.menu-title {
    flex: 1;
}

.menu-badge {
    margin-left: auto;
}

/* Main Content */
.app-wrapper {
    margin-left: 265px;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    transition: all 0.3s ease;
}

.app-content {
    padding: 0;
}

.app-container {
    max-width: none;
    padding: 0 1.5rem;
}

/* Toolbar */
.toolbar {
    padding: 1.5rem 0;
    background-color: var(--kt-white);
    border-bottom: 1px solid var(--kt-gray-200);
    margin-bottom: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0 20px 0 rgba(76, 87, 125, 0.02);
    transition: all 0.3s ease;
    background-color: var(--kt-white);
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 28px 0 rgba(76, 87, 125, 0.06);
}

.card-flush {
    border-radius: 0.75rem;
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--kt-gray-200);
    padding: 1.5rem 1.5rem 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--kt-gray-900);
}

.card-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--kt-gray-900);
}

/* Statistics Cards */
.bg-primary {
    background: linear-gradient(135deg, var(--kt-primary) 0%, #2884EF 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, var(--kt-success) 0%, #0BB7AF 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--kt-info) 0%, #7239EA 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--kt-warning) 0%, #F49917 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--kt-danger) 0%, #E73E4E 100%) !important;
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.35rem 0.65rem;
    border-radius: 0.425rem;
}

.badge-light-primary {
    background-color: var(--kt-primary-light);
    color: var(--kt-primary);
}

.badge-light-success {
    background-color: var(--kt-success-light);
    color: var(--kt-success);
}

.badge-light-info {
    background-color: var(--kt-info-light);
    color: var(--kt-info);
}

.badge-light-warning {
    background-color: var(--kt-warning-light);
    color: var(--kt-warning);
}

.badge-light-danger {
    background-color: var(--kt-danger-light);
    color: var(--kt-danger);
}

/* Text Colors */
.text-primary {
    color: var(--kt-primary) !important;
}

.text-success {
    color: var(--kt-success) !important;
}

.text-info {
    color: var(--kt-info) !important;
}

.text-warning {
    color: var(--kt-warning) !important;
}

.text-danger {
    color: var(--kt-danger) !important;
}

.text-dark {
    color: var(--kt-gray-900) !important;
}

.text-muted {
    color: var(--kt-gray-500) !important;
}

.text-gray-400 {
    color: var(--kt-gray-400) !important;
}

.text-gray-500 {
    color: var(--kt-gray-500) !important;
}

.text-gray-600 {
    color: var(--kt-gray-600) !important;
}

.text-gray-700 {
    color: var(--kt-gray-700) !important;
}

.text-gray-800 {
    color: var(--kt-gray-800) !important;
}

/* Symbol Styles */
.symbol {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.475rem;
    overflow: hidden;
    position: relative;
}

.symbol-35px {
    width: 35px;
    height: 35px;
}

.symbol-45px {
    width: 45px;
    height: 45px;
}

.symbol-50px {
    width: 50px;
    height: 50px;
}

.symbol-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-weight: 600;
    font-size: 0.85rem;
}

/* Timeline Styles */
.timeline-label {
    position: relative;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 17px;
    top: 35px;
    bottom: -24px;
    width: 1px;
    background-color: var(--kt-gray-300);
}

.timeline-label {
    min-width: 80px;
    font-size: 0.75rem;
    color: var(--kt-gray-600);
    margin-right: 1rem;
    margin-top: 0.25rem;
}

.timeline-badge {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--kt-white);
    border: 2px solid var(--kt-gray-300);
    border-radius: 50%;
    margin-right: 1rem;
    z-index: 1;
}

.timeline-content {
    flex: 1;
    padding-top: 0.25rem;
}

/* Table Styles */
.table {
    color: var(--kt-gray-700);
    border-collapse: separate;
    border-spacing: 0;
}

.table-row-dashed tbody tr {
    border-bottom: 1px dashed var(--kt-gray-300);
}

.table-row-gray-300 tbody tr {
    border-bottom: 1px solid var(--kt-gray-300);
}

.table thead th {
    background-color: transparent;
    border-bottom: 1px solid var(--kt-gray-300);
    font-weight: 600;
    color: var(--kt-gray-600);
    padding: 1rem 0.75rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
    font-size: 0.925rem;
}

.table tbody tr:hover {
    background-color: var(--kt-gray-100);
}

/* Rating Styles */
.rating {
    display: flex;
    align-items: center;
}

.rating-label {
    color: var(--kt-gray-300);
    margin-right: 2px;
}

.rating-label.checked {
    color: var(--kt-warning);
}

/* Button Styles */
.btn {
    border-radius: 0.475rem;
    font-weight: 600;
    font-size: 0.925rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.15s ease;
    border: 1px solid transparent;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0 20px 0 rgba(76, 87, 125, 0.12);
}

.btn-primary {
    background-color: var(--kt-primary);
    border-color: var(--kt-primary);
    color: var(--kt-white);
}

.btn-primary:hover {
    background-color: #2884EF;
    border-color: #2884EF;
}

.btn-light-primary {
    background-color: var(--kt-primary-light);
    border-color: var(--kt-primary-light);
    color: var(--kt-primary);
}

.btn-light {
    background-color: var(--kt-gray-100);
    border-color: var(--kt-gray-100);
    color: var(--kt-gray-700);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.btn-icon {
    width: 35px;
    height: 35px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-bg-light {
    background-color: var(--kt-gray-100);
    border-color: var(--kt-gray-100);
}

.btn-active-color-primary:hover {
    color: var(--kt-primary);
    background-color: var(--kt-primary-light);
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .app-sidebar {
        transform: translateX(-100%);
    }

    .app-wrapper {
        margin-left: 0;
    }

    .app-sidebar.drawer-on {
        transform: translateX(0);
    }

    .app-sidebar-mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998;
        display: none;
    }

    .app-sidebar.drawer-on + .app-sidebar-mobile-overlay {
        display: block;
    }
}

@media (max-width: 767.98px) {
    .app-container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .toolbar {
        padding: 1rem 0;
    }

    .page-heading {
        font-size: 1.1rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }
}

/* Utility Classes */
.fw-bold {
    font-weight: 700 !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

.fs-1 { font-size: 2.5rem !important; }
.fs-2 { font-size: 2rem !important; }
.fs-3 { font-size: 1.75rem !important; }
.fs-4 { font-size: 1.5rem !important; }
.fs-5 { font-size: 1.25rem !important; }
.fs-6 { font-size: 1rem !important; }
.fs-7 { font-size: 0.85rem !important; }

.min-h-auto {
    min-height: auto !important;
}

.h-md-50 {
    height: 50% !important;
}

.min-w-100px {
    min-width: 100px !important;
}

.min-w-200px {
    min-width: 200px !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.animation-blink {
    animation: blink 1.5s infinite;
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar Styles */
.app-sidebar-wrapper::-webkit-scrollbar {
    width: 6px;
}

.app-sidebar-wrapper::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.app-sidebar-wrapper::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.app-sidebar-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Separator */
.separator {
    height: 1px;
    background-color: var(--kt-gray-200);
    margin: 0.5rem 0;
}

/* Bullet */
.bullet {
    display: inline-block;
    border-radius: 50%;
}

.bullet-dot {
    width: 6px;
    height: 6px;
}

.bg-success {
    background-color: var(--kt-success) !important;
}

/* Menu Dropdown */
.menu-sub {
    background-color: var(--kt-white);
    border-radius: 0.75rem;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    border: 1px solid var(--kt-gray-200);
    padding: 0.5rem 0;
    min-width: 200px;
}

.menu-sub .menu-item {
    margin: 0;
}

.menu-sub .menu-link {
    padding: 0.75rem 1rem;
    margin: 0;
    color: var(--kt-gray-700);
    border-radius: 0;
}

.menu-sub .menu-link:hover {
    background-color: var(--kt-gray-100);
    color: var(--kt-primary);
    transform: none;
}

/* Additional Metronic Enhancements */
.app-sidebar-minimize .app-sidebar {
    width: 70px;
}

.app-sidebar-minimize .menu-title,
.app-sidebar-minimize .menu-badge {
    display: none;
}

.app-sidebar-minimize .app-wrapper {
    margin-left: 70px;
}

/* Hover effects for icons */
.fas, .far {
    transition: all 0.3s ease;
}

.card:hover .fas,
.card:hover .far {
    transform: scale(1.05);
}

/* Enhanced shadows */
.shadow {
    box-shadow: 0 0 20px 0 rgba(76, 87, 125, 0.02) !important;
}

/* Border utilities */
.border-bottom {
    border-bottom: 1px solid var(--kt-gray-200) !important;
}
