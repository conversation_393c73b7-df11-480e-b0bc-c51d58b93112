/* Custom Dashboard Styles */

/* Body and Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
    padding-top: 56px; /* Account for fixed navbar */
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.15);
    font-weight: 600;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content */
main {
    margin-left: 0;
    padding-top: 20px;
}

@media (min-width: 768px) {
    main {
        margin-left: 240px;
    }
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* Colored Border Cards */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* Text Colors */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-500 {
    color: #858796 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Statistics Cards */
.card-body .h5 {
    font-size: 2rem;
    font-weight: 700;
}

.text-xs {
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Navigation Bar */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Chart Container */
.chart-area {
    position: relative;
    height: 300px;
}

/* Recent Activities */
.recent-activities .activity-item {
    padding: 15px 0;
    border-bottom: 1px solid #e3e6f0;
}

.recent-activities .activity-item:last-child {
    border-bottom: none;
}

/* Responsive Design */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    main {
        margin-left: 0;
    }
    
    .card-body .h5 {
        font-size: 1.5rem;
    }
    
    .col-xl-3.col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Card Header */
.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 15px 15px 0 0 !important;
}

.card-header h6 {
    color: #5a5c69;
    font-weight: 700;
}

/* Utility Classes */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.border-bottom {
    border-bottom: 1px solid #e3e6f0 !important;
}

/* Icon Styling */
.fas, .far {
    transition: all 0.3s ease;
}

.card:hover .fas,
.card:hover .far {
    transform: scale(1.1);
}

/* Dropdown Menu */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 10px;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

/* Mobile Responsive Adjustments */
@media (max-width: 576px) {
    .btn-toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-group {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}
