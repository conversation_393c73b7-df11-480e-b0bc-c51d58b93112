# Modern Dashboard - CodeIgniter 3

A beautiful, responsive admin dashboard built with CodeIgniter 3, Bootstrap 5, and modern web technologies.

## Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI**: Clean and professional interface with Bootstrap 5
- **Interactive Charts**: Dynamic data visualization with Chart.js
- **Statistics Widgets**: Animated counters and key metrics display
- **Clean Architecture**: Follows CodeIgniter 3 MVC pattern
- **Font Awesome Icons**: Beautiful iconography throughout the interface
- **Smooth Animations**: CSS transitions and JavaScript animations
- **Mobile-First**: Optimized for mobile devices with collapsible sidebar

## Screenshots

The dashboard includes:
- Statistics cards with animated counters
- Interactive line chart for sales data
- Recent activities feed
- Top products table with action buttons
- Responsive navigation with collapsible sidebar
- Professional color scheme and typography

## Installation

### Prerequisites

- PHP 7.2 or higher
- Web server (Apache/Nginx)
- MySQL (optional, for database features)

### Setup Instructions

1. **Clone or Download**: The CodeIgniter 3 framework is already included in this installation.

2. **Configure Base URL**: 
   - Open `application/config/config.php`
   - Update the `$config['base_url']` to match your domain/path

3. **Database Configuration** (Optional):
   - Open `application/config/database.php`
   - Update database credentials if you plan to use database features
   - Default settings are configured for WAMP/XAMPP

4. **Web Server Setup**:
   - Ensure your web server points to the project root directory
   - Make sure mod_rewrite is enabled for clean URLs

5. **File Permissions**:
   - Ensure `application/logs/` and `application/cache/` are writable

## File Structure

```
anoop/
├── application/
│   ├── controllers/
│   │   └── Dashboard.php          # Main dashboard controller
│   ├── views/
│   │   └── dashboard/
│   │       └── index.php          # Dashboard view template
│   ├── config/
│   │   ├── config.php             # Main configuration
│   │   ├── database.php           # Database configuration
│   │   ├── routes.php             # URL routing
│   │   └── autoload.php           # Auto-loaded libraries
│   └── [other CI directories]
├── assets/
│   ├── css/
│   │   └── dashboard.css          # Custom dashboard styles
│   ├── js/
│   │   └── dashboard.js           # Dashboard JavaScript
│   └── img/                       # Images directory
├── system/                        # CodeIgniter core files
├── .htaccess                      # URL rewriting rules
└── index.php                     # Main entry point
```

## Customization

### Adding New Pages

1. Create a new controller in `application/controllers/`
2. Create corresponding views in `application/views/`
3. Add navigation links in the sidebar section of `dashboard/index.php`

### Modifying the Dashboard

- **Statistics Cards**: Update the data in `Dashboard.php` controller
- **Charts**: Modify the chart data and configuration in the view file
- **Styling**: Edit `assets/css/dashboard.css` for custom styles
- **Functionality**: Add JavaScript features in `assets/js/dashboard.js`

### Color Scheme

The dashboard uses a professional color palette:
- Primary: #4e73df (Blue)
- Success: #1cc88a (Green)
- Info: #36b9cc (Cyan)
- Warning: #f6c23e (Yellow)
- Danger: #e74a3b (Red)

## Technologies Used

- **Backend**: CodeIgniter 3.1.13
- **Frontend**: Bootstrap 5.3.0
- **Icons**: Font Awesome 6.4.0
- **Charts**: Chart.js (latest)
- **Fonts**: Segoe UI, system fonts

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Performance Features

- Compressed CSS and JavaScript
- Optimized images
- Efficient database queries
- Cached static assets
- Minified external libraries via CDN

## Security Features

- XSS protection
- CSRF protection (CodeIgniter built-in)
- SQL injection prevention
- Secure headers via .htaccess
- Input validation and sanitization

## Development

### Local Development

1. Use WAMP, XAMPP, or similar local server
2. Access via `http://localhost/maps/anoop/`
3. Enable error reporting in development environment

### Production Deployment

1. Update `ENVIRONMENT` constant in `index.php`
2. Configure proper database credentials
3. Set up SSL certificate
4. Enable caching and compression
5. Regular security updates

## Troubleshooting

### Common Issues

1. **404 Errors**: Check .htaccess file and mod_rewrite
2. **CSS/JS Not Loading**: Verify base_url configuration
3. **Database Errors**: Check database credentials and server
4. **Permission Errors**: Ensure proper file permissions

### Support

For issues and questions:
1. Check CodeIgniter 3 documentation
2. Verify configuration settings
3. Check browser console for JavaScript errors
4. Review server error logs

## License

This project is built on CodeIgniter 3, which is licensed under the MIT License.

## Credits

- CodeIgniter Framework
- Bootstrap CSS Framework
- Font Awesome Icons
- Chart.js Library
- Modern dashboard design patterns

---

**Note**: This is a demonstration dashboard with sample data. In a production environment, you would connect to a real database and implement proper authentication and authorization systems.
