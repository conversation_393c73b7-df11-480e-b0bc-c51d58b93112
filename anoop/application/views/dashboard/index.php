<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?> - Metronic Admin</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- ApexCharts -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Custom CSS -->
    <link href="<?php echo base_url('assets/css/dashboard.css'); ?>" rel="stylesheet">
</head>
<body class="metronic-body"
    data-kt-app-layout="dark-sidebar"
    data-kt-app-header-fixed="true"
    data-kt-app-sidebar-enabled="true"
    data-kt-app-sidebar-fixed="true"
    data-kt-app-sidebar-hoverable="true"
    data-kt-app-sidebar-push-header="true"
    data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true"
    data-kt-app-toolbar-enabled="true"
    data-kt-app-content-full-height="true"
    data-kt-app-aside-enabled="true"
    data-kt-app-aside-fixed="true"
    data-kt-app-aside-push-header="true"
    data-kt-app-aside-push-toolbar="true"
    id="kt_app_body"
>
    <!-- App Layout -->
    <div class="d-flex flex-column flex-root app-root" id="kt_app_root">
        <!-- App Page -->
        <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
            <!-- Header -->
            <div id="kt_app_header" class="app-header">
                <div class="app-container container-fluid d-flex align-items-stretch justify-content-between">
                    <!-- Header Logo -->
                    <div class="d-flex align-items-center d-lg-none ms-n3 me-1" data-kt-swapper="true" data-kt-swapper-mode="prepend" data-kt-swapper-parent="{default: '#kt_app_sidebar_menu', 'lg': '#kt_app_navbar_wrapper'}">
                        <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_sidebar_mobile_toggle">
                            <i class="fas fa-bars fs-2"></i>
                        </div>
                    </div>

                    <!-- Header Brand -->
                    <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                        <a href="<?php echo base_url(); ?>" class="d-lg-none">
                            <img alt="Logo" src="<?php echo base_url('assets/img/logo.svg'); ?>" class="h-30px" />
                        </a>
                    </div>

                    <!-- Header Navbar -->
                    <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1" id="kt_app_navbar_wrapper">
                        <!-- Page Title -->
                        <div class="app-navbar-item ms-1 ms-lg-3" id="kt_header_search">
                            <div class="d-flex align-items-center position-relative my-1">
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Dashboard
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">Welcome back, Admin!</span>
                                </h1>
                            </div>
                        </div>

                        <!-- Header Menu -->
                        <div class="app-navbar flex-shrink-0">
                            <!-- Search -->
                            <div class="app-navbar-item ms-1 ms-lg-3">
                                <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                                    <i class="fas fa-search fs-2"></i>
                                </div>
                            </div>

                            <!-- Notifications -->
                            <div class="app-navbar-item ms-1 ms-lg-3">
                                <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px position-relative" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                                    <i class="fas fa-bell fs-2"></i>
                                    <span class="bullet bullet-dot bg-success h-6px w-6px position-absolute translate-middle top-0 start-50 animation-blink"></span>
                                </div>
                            </div>

                            <!-- User Menu -->
                            <div class="app-navbar-item ms-1 ms-lg-3" id="kt_header_user_menu_toggle">
                                <div class="cursor-pointer symbol symbol-35px symbol-md-40px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                                    <div class="symbol-label fs-3 bg-light-primary text-primary">A</div>
                                </div>

                                <!-- User Menu Dropdown -->
                                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px" data-kt-menu="true">
                                    <div class="menu-item px-3">
                                        <div class="menu-content d-flex align-items-center px-3">
                                            <div class="symbol symbol-50px me-5">
                                                <div class="symbol-label fs-3 bg-light-primary text-primary">A</div>
                                            </div>
                                            <div class="d-flex flex-column">
                                                <div class="fw-bold d-flex align-items-center fs-5">Admin User</div>
                                                <a href="#" class="fw-semibold text-muted text-hover-primary fs-7"><EMAIL></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="separator my-2"></div>
                                    <div class="menu-item px-5">
                                        <a href="#" class="menu-link px-5">My Profile</a>
                                    </div>
                                    <div class="menu-item px-5">
                                        <a href="#" class="menu-link px-5">Settings</a>
                                    </div>
                                    <div class="separator my-2"></div>
                                    <div class="menu-item px-5">
                                        <a href="#" class="menu-link px-5">Sign Out</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
                <!-- Sidebar Logo -->
                <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">
                    <a href="<?php echo base_url(); ?>">
                        <img alt="Logo" src="<?php echo base_url('assets/img/logo-dark.svg'); ?>" class="h-25px app-sidebar-logo-default" />
                        <img alt="Logo" src="<?php echo base_url('assets/img/logo-light.svg'); ?>" class="h-20px app-sidebar-logo-minimize" />
                    </a>

                    <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
                        <i class="fas fa-angle-left fs-5 rotate-180"></i>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
                    <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5 py-5" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="30px">
                        <div class="menu menu-column menu-rounded menu-sub-indention px-3" id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                            <!-- Dashboard -->
                            <div class="menu-item">
                                <a class="menu-link active" href="<?php echo base_url(); ?>">
                                    <span class="menu-icon">
                                        <i class="fas fa-tachometer-alt fs-2"></i>
                                    </span>
                                    <span class="menu-title">Dashboard</span>
                                </a>
                            </div>

                            <!-- Users -->
                            <div class="menu-item">
                                <a class="menu-link" href="#">
                                    <span class="menu-icon">
                                        <i class="fas fa-users fs-2"></i>
                                    </span>
                                    <span class="menu-title">Users</span>
                                </a>
                            </div>

                            <!-- Orders -->
                            <div class="menu-item">
                                <a class="menu-link" href="#">
                                    <span class="menu-icon">
                                        <i class="fas fa-shopping-cart fs-2"></i>
                                    </span>
                                    <span class="menu-title">Orders</span>
                                    <span class="menu-badge">
                                        <span class="badge badge-success">5</span>
                                    </span>
                                </a>
                            </div>

                            <!-- Products -->
                            <div class="menu-item">
                                <a class="menu-link" href="#">
                                    <span class="menu-icon">
                                        <i class="fas fa-box fs-2"></i>
                                    </span>
                                    <span class="menu-title">Products</span>
                                </a>
                            </div>

                            <!-- Analytics -->
                            <div class="menu-item">
                                <a class="menu-link" href="#">
                                    <span class="menu-icon">
                                        <i class="fas fa-chart-bar fs-2"></i>
                                    </span>
                                    <span class="menu-title">Analytics</span>
                                </a>
                            </div>

                            <!-- Reports -->
                            <div class="menu-item">
                                <a class="menu-link" href="#">
                                    <span class="menu-icon">
                                        <i class="fas fa-file-alt fs-2"></i>
                                    </span>
                                    <span class="menu-title">Reports</span>
                                </a>
                            </div>

                            <!-- Settings -->
                            <div class="menu-item">
                                <a class="menu-link" href="#">
                                    <span class="menu-icon">
                                        <i class="fas fa-cog fs-2"></i>
                                    </span>
                                    <span class="menu-title">Settings</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
                <div id="kt_app_content" class="app-content flex-column-fluid">
                    <div id="kt_app_content_container" class="app-container container-xxl">
                        <!-- Toolbar -->
                        <div class="toolbar" id="kt_toolbar">
                            <div class="container-fluid d-flex flex-stack">
                                <div class="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
                                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                        Dashboard Overview
                                        <span class="page-desc text-muted fs-7 fw-semibold pt-1">Monitor your business performance</span>
                                    </h1>
                                </div>
                                <div class="d-flex align-items-center gap-2 gap-lg-3">
                                    <a href="#" class="btn btn-sm fw-bold btn-secondary" data-bs-toggle="modal" data-bs-target="#kt_modal_create_app">Export</a>
                                    <a href="#" class="btn btn-sm fw-bold btn-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_new_target">Add New</a>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                            <?php foreach($stats as $stat): ?>
                            <div class="col-md-6 col-lg-6 col-xl-3">
                                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50 mb-5 mb-xl-10 bg-<?php echo $stat['color']; ?>">
                                    <div class="card-header pt-5">
                                        <div class="card-title d-flex flex-column">
                                            <div class="d-flex align-items-center">
                                                <span class="fs-4 fw-semibold text-white me-1 align-self-start">
                                                    <?php echo is_numeric($stat['value']) ? '$' . number_format($stat['value'], 2) : $stat['value']; ?>
                                                </span>
                                                <span class="badge badge-light-success fs-base">
                                                    <i class="fas fa-arrow-<?php echo $stat['growth_type'] == 'positive' ? 'up' : 'down'; ?> fs-5 text-<?php echo $stat['growth_type'] == 'positive' ? 'success' : 'danger'; ?> ms-n1"></i>
                                                    <?php echo $stat['growth']; ?>
                                                </span>
                                            </div>
                                            <span class="text-white opacity-75 pt-1 fw-semibold fs-6"><?php echo $stat['title']; ?></span>
                                        </div>
                                    </div>
                                    <div class="card-body d-flex align-items-end pt-0">
                                        <div class="d-flex align-items-center flex-column mt-3 w-100">
                                            <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                                <span><?php echo $stat['description']; ?></span>
                                                <span><i class="fas fa-<?php echo $stat['icon']; ?> fs-6"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Charts and Analytics Row -->
                        <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                            <!-- Sales Chart -->
                            <div class="col-xl-8">
                                <div class="card card-flush h-xl-100">
                                    <div class="card-header pt-5">
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Sales Analytics</span>
                                            <span class="text-gray-400 mt-1 fw-semibold fs-6">Revenue and Orders Performance</span>
                                        </h3>
                                        <div class="card-toolbar">
                                            <div class="btn-group" data-kt-buttons="true">
                                                <label class="btn btn-sm btn-outline btn-outline-dashed btn-active-light-primary active">
                                                    <input class="btn-check" type="radio" name="chart_period" value="month" checked="checked" />
                                                    Month
                                                </label>
                                                <label class="btn btn-sm btn-outline btn-outline-dashed btn-active-light-primary">
                                                    <input class="btn-check" type="radio" name="chart_period" value="week" />
                                                    Week
                                                </label>
                                                <label class="btn btn-sm btn-outline btn-outline-dashed btn-active-light-primary">
                                                    <input class="btn-check" type="radio" name="chart_period" value="day" />
                                                    Day
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body pt-6">
                                        <div id="kt_charts_widget_1" class="min-h-auto ps-4 pe-6" style="height: 300px"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activities -->
                            <div class="col-xl-4">
                                <div class="card card-flush h-xl-100">
                                    <div class="card-header pt-5">
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Recent Activities</span>
                                            <span class="text-gray-400 mt-1 fw-semibold fs-6">Latest user actions</span>
                                        </h3>
                                        <div class="card-toolbar">
                                            <a href="#" class="btn btn-sm btn-light">View All</a>
                                        </div>
                                    </div>
                                    <div class="card-body pt-5">
                                        <div class="timeline-label">
                                            <?php foreach($recent_activities as $index => $activity): ?>
                                            <div class="timeline-item">
                                                <div class="timeline-label fw-bold text-gray-800 fs-6"><?php echo $activity['time']; ?></div>
                                                <div class="timeline-badge">
                                                    <i class="fas fa-<?php echo $activity['icon']; ?> text-<?php echo $activity['color']; ?>"></i>
                                                </div>
                                                <div class="fw-semibold timeline-content text-gray-800 ps-3">
                                                    <div class="d-flex align-items-center">
                                                        <div class="symbol symbol-35px me-3">
                                                            <div class="symbol-label bg-light-<?php echo $activity['color']; ?> text-<?php echo $activity['color']; ?> fw-bold">
                                                                <?php echo $activity['avatar']; ?>
                                                            </div>
                                                        </div>
                                                        <div class="flex-grow-1">
                                                            <span class="fw-bold text-gray-800"><?php echo $activity['user']; ?></span>
                                                            <span class="text-muted"><?php echo $activity['action']; ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Top Products Table -->
                        <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                            <div class="col-xl-12">
                                <div class="card card-flush h-xl-100">
                                    <div class="card-header pt-5">
                                        <h3 class="card-title align-items-start flex-column">
                                            <span class="card-label fw-bold text-dark">Top Selling Products</span>
                                            <span class="text-gray-400 mt-1 fw-semibold fs-6">Best performing products this month</span>
                                        </h3>
                                        <div class="card-toolbar">
                                            <div class="d-flex align-items-center gap-2">
                                                <a href="#" class="btn btn-sm btn-flex btn-light-primary">
                                                    <i class="fas fa-download fs-4 me-1"></i>Export
                                                </a>
                                                <a href="#" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-plus fs-4 me-1"></i>Add Product
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body pt-6">
                                        <div class="table-responsive">
                                            <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                                                <thead>
                                                    <tr class="fw-bold text-muted">
                                                        <th class="min-w-200px">Product</th>
                                                        <th class="min-w-100px text-end">Sales</th>
                                                        <th class="min-w-100px text-end">Revenue</th>
                                                        <th class="min-w-100px text-center">Rating</th>
                                                        <th class="min-w-100px text-center">Status</th>
                                                        <th class="min-w-100px text-end">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach($top_products as $product): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-45px me-5">
                                                                    <div class="symbol-label bg-light-primary text-primary fw-bold">
                                                                        <?php echo substr($product['name'], 0, 2); ?>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex justify-content-start flex-column">
                                                                    <a href="#" class="text-dark fw-bold text-hover-primary fs-6"><?php echo $product['name']; ?></a>
                                                                    <span class="text-muted fw-semibold text-muted d-block fs-7"><?php echo $product['category']; ?></span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="text-end">
                                                            <div class="d-flex flex-column w-100 me-2">
                                                                <div class="d-flex flex-stack mb-2">
                                                                    <span class="text-muted me-2 fs-7 fw-semibold"><?php echo number_format($product['sales']); ?></span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="text-end">
                                                            <span class="text-dark fw-bold d-block fs-6">$<?php echo number_format($product['revenue'], 2); ?></span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="rating">
                                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                                    <div class="rating-label <?php echo $i <= floor($product['rating']) ? 'checked' : ''; ?>">
                                                                        <i class="fas fa-star fs-6"></i>
                                                                    </div>
                                                                <?php endfor; ?>
                                                                <span class="text-muted fs-7 fw-semibold ms-1"><?php echo $product['rating']; ?></span>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge badge-light-<?php echo $product['status_color']; ?> fs-7 fw-semibold"><?php echo $product['status']; ?></span>
                                                        </td>
                                                        <td class="text-end">
                                                            <div class="d-flex justify-content-end flex-shrink-0">
                                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1">
                                                                    <i class="fas fa-eye fs-3"></i>
                                                                </a>
                                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1">
                                                                    <i class="fas fa-edit fs-3"></i>
                                                                </a>
                                                                <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm">
                                                                    <i class="fas fa-trash fs-3"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo base_url('assets/js/dashboard.js'); ?>"></script>

    <!-- Chart initialization -->
    <script>
        // ApexCharts Configuration
        var options = {
            series: [{
                name: 'Revenue',
                type: 'area',
                data: <?php echo json_encode($sales_chart['datasets'][0]['data']); ?>
            }, {
                name: 'Orders',
                type: 'line',
                data: <?php echo json_encode($sales_chart['datasets'][1]['data']); ?>
            }],
            chart: {
                height: 300,
                type: 'line',
                toolbar: {
                    show: false
                }
            },
            stroke: {
                curve: 'smooth',
                width: [0, 3]
            },
            fill: {
                type: ['gradient', 'solid'],
                gradient: {
                    shadeIntensity: 1,
                    type: "vertical",
                    colorStops: [
                        {
                            offset: 0,
                            color: "#3699FF",
                            opacity: 0.4
                        },
                        {
                            offset: 100,
                            color: "#3699FF",
                            opacity: 0.1
                        }
                    ]
                }
            },
            colors: ['#3699FF', '#1BC5BD'],
            labels: <?php echo json_encode($sales_chart['labels']); ?>,
            markers: {
                size: 0
            },
            yaxis: [
                {
                    title: {
                        text: 'Revenue ($)',
                    },
                },
                {
                    opposite: true,
                    title: {
                        text: 'Orders'
                    }
                }
            ],
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function (y) {
                        if (typeof y !== "undefined") {
                            return y.toFixed(0) + " points";
                        }
                        return y;
                    }
                }
            },
            grid: {
                borderColor: '#f1f1f1',
            }
        };

        var chart = new ApexCharts(document.querySelector("#kt_charts_widget_1"), options);
        chart.render();

        // Initialize Metronic components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize sidebar toggle
            const sidebarToggle = document.getElementById('kt_app_sidebar_toggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    document.body.classList.toggle('app-sidebar-minimize');
                });
            }

            // Initialize mobile sidebar toggle
            const mobileSidebarToggle = document.getElementById('kt_app_sidebar_mobile_toggle');
            if (mobileSidebarToggle) {
                mobileSidebarToggle.addEventListener('click', function() {
                    document.body.classList.toggle('app-sidebar-on');
                });
            }

            // Initialize menu items
            const menuItems = document.querySelectorAll('.menu-link');
            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Remove active class from all items
                    menuItems.forEach(mi => mi.classList.remove('active'));
                    // Add active class to clicked item
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
