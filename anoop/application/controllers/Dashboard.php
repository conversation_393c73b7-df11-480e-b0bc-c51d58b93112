<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        // Load any models or libraries here if needed
    }

    public function index()
    {
        // Sample data for dashboard widgets
        $data['total_users'] = 1247;
        $data['total_orders'] = 856;
        $data['total_revenue'] = 45678.90;
        $data['total_products'] = 324;
        
        // Sample chart data
        $data['monthly_sales'] = array(
            'January' => 12500,
            'February' => 15800,
            'March' => 18200,
            'April' => 22100,
            'May' => 19800,
            'June' => 25600
        );
        
        // Sample recent activities
        $data['recent_activities'] = array(
            array(
                'user' => '<PERSON>',
                'action' => 'placed a new order',
                'time' => '2 minutes ago',
                'icon' => 'shopping-cart'
            ),
            array(
                'user' => '<PERSON> Smith',
                'action' => 'updated profile',
                'time' => '15 minutes ago',
                'icon' => 'user'
            ),
            array(
                'user' => '<PERSON>',
                'action' => 'added new product',
                'time' => '1 hour ago',
                'icon' => 'plus-circle'
            ),
            array(
                'user' => '<PERSON>',
                'action' => 'completed payment',
                'time' => '2 hours ago',
                'icon' => 'credit-card'
            )
        );
        
        // Sample top products
        $data['top_products'] = array(
            array('name' => 'Wireless Headphones', 'sales' => 156, 'revenue' => 15600),
            array('name' => 'Smartphone Case', 'sales' => 234, 'revenue' => 4680),
            array('name' => 'Bluetooth Speaker', 'sales' => 89, 'revenue' => 8900),
            array('name' => 'USB Cable', 'sales' => 445, 'revenue' => 2225),
            array('name' => 'Power Bank', 'sales' => 123, 'revenue' => 6150)
        );
        
        $data['page_title'] = 'Dashboard';
        
        $this->load->view('dashboard/index', $data);
    }
}
